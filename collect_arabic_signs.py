#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Enhanced Data Collection System for Arabic Sign Language Recognition
نظام جمع البيانات المحسن للتعرف على لغة الإشارة العربية
"""

import cv2
import mediapipe as mp
import numpy as np
import os
import json
from datetime import datetime
import time

class ArabicSignLanguageCollector:
    def __init__(self):
        # Initialize MediaPipe
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.8,
            min_tracking_confidence=0.7
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Arabic Sign Language Words - الكلمات المستهدفة
        self.word_pairs = [
            ("السلام عليكم", "As-salamu alaykum", "اليد اليمنى مفتوحة تتحرك من اليمين لليسار + اليد اليسرى على القلب"),
            ("وعليكم السلام ورحمة الله وبركاته", "Wa alaykumu s-salamu", "كلتا اليدين مفتوحتين، حركة دائرية للأعلى والخارج"),
            ("لو", "Law", "السبابة للأعلى، حركة يمين-يسار (استفهام)"),
            ("العالم", "Al-alam", "اليدين في شكل كرة، حركة دائرية حول بعضهما"),
            ("كله", "Kullu", "افتح اليدين للخارج ثم اجمعهما"),
            ("سكت", "Sakat", "السبابة على الشفاه (إشارة الصمت)"),
            ("هتعمل", "Hat'amal", "قبضتين تتحركان للأمام والخلف (حركة العمل)"),
            ("ايه", "Eh", "ارفع اليدين بأصابع مفتوحة (استفهام)"),
            ("هتكلم", "Hatkallam", "اليد أمام الفم مع تحريك الأصابع (كلام)"),
            ("بايدي", "Bi-yadi", "ارفع كلتا اليدين مع فتح الكفين"),
            ("وهخلي", "Wa-hakhalli", "من قبضة إلى يد مفتوحة للأمام (عطاء/صنع)"),
            ("يسمعني", "Yasma'ni", "اليد خلف الأذن ثم للأمام (استماع)")
        ]
        
        # Create directories
        self.data_dir = "arabic_sign_data"
        self.images_dir = "arabic_sign_images"
        self.landmarks_dir = "arabic_landmarks"
        
        for directory in [self.data_dir, self.images_dir, self.landmarks_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 Created directory: {directory}")
    
    def extract_landmarks(self, results):
        """Extract hand landmarks"""
        landmarks = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                hand_points = []
                for landmark in hand_landmarks.landmark:
                    hand_points.extend([landmark.x, landmark.y, landmark.z])
                landmarks.extend(hand_points)
        
        # Pad to 126 elements (21 points × 3 coordinates × 2 hands)
        while len(landmarks) < 126:
            landmarks.append(0.0)
        
        return landmarks[:126]
    
    def collect_word_data(self, arabic_word, english_word, movement_desc, samples_count=50):
        """Collect data for one word"""
        print(f"\n🎯 جمع البيانات للكلمة: {arabic_word}")
        print(f"   الإنجليزية: {english_word}")
        print(f"   الحركة: {movement_desc}")
        print(f"   العينات المطلوبة: {samples_count}")
        print("="*70)
        
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Cannot access camera")
            return False
        
        collected_samples = 0
        word_data = []
        
        print("📋 التعليمات:")
        print("   - تأكد من الإضاءة الجيدة")
        print("   - ضع يديك بوضوح أمام الكاميرا")
        print("   - اضغط SPACE لالتقاط العينة")
        print("   - اضغط ESC للانتهاء من هذه الكلمة")
        print("   - اضغط Q للخروج من البرنامج")
        print("="*70)
        
        input("اضغط Enter عندما تكون جاهز...")
        
        while collected_samples < samples_count:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            # Draw hand landmarks
            hands_detected = False
            if results.multi_hand_landmarks:
                hands_detected = True
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS,
                        self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=3),
                        self.mp_drawing.DrawingSpec(color=(255, 0, 0), thickness=3)
                    )
            
            # Add info overlay
            self.add_info_overlay(frame, arabic_word, english_word, movement_desc, 
                                collected_samples, samples_count, hands_detected)
            
            cv2.imshow('Arabic Sign Language Data Collection', frame)
            
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord(' ') and hands_detected:  # Space to capture
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                
                # Extract landmarks
                landmarks = self.extract_landmarks(results)
                
                # Save image
                img_filename = f"{english_word.replace(' ', '_')}_{collected_samples+1:03d}_{timestamp}.jpg"
                img_path = os.path.join(self.images_dir, img_filename)
                cv2.imwrite(img_path, frame)
                
                # Create sample data
                sample_data = {
                    'word_arabic': arabic_word,
                    'word_english': english_word,
                    'sample_id': collected_samples + 1,
                    'timestamp': timestamp,
                    'image_filename': img_filename,
                    'landmarks': landmarks,
                    'hand_detected': True,
                    'movement_description': movement_desc
                }
                
                # Save individual landmark file
                landmark_filename = f"{english_word.replace(' ', '_')}_{collected_samples+1:03d}_{timestamp}.json"
                landmark_path = os.path.join(self.landmarks_dir, landmark_filename)
                
                with open(landmark_path, 'w', encoding='utf-8') as f:
                    json.dump(sample_data, f, ensure_ascii=False, indent=2)
                
                word_data.append(sample_data)
                collected_samples += 1
                
                print(f"✅ تم حفظ العينة {collected_samples}/{samples_count}")
                
                # Brief pause to avoid accidental multiple captures
                time.sleep(0.5)
            
            elif key == 27:  # ESC to finish this word
                break
            elif key == ord('q'):  # Q to quit completely
                cap.release()
                cv2.destroyAllWindows()
                return False
        
        cap.release()
        cv2.destroyAllWindows()
        
        # Save word data
        word_filename = f"{english_word.replace(' ', '_')}_complete_data.json"
        word_filepath = os.path.join(self.data_dir, word_filename)
        with open(word_filepath, 'w', encoding='utf-8') as f:
            json.dump(word_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم الانتهاء من كلمة '{arabic_word}' - {collected_samples} عينة")
        return True
    
    def add_info_overlay(self, frame, arabic_word, english_word, movement_desc, 
                        collected, total, hands_detected):
        """Add information overlay to frame"""
        height, width = frame.shape[:2]
        
        # Semi-transparent background
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (width-10, 200), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Arabic word (larger font)
        cv2.putText(frame, arabic_word, (20, 50),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 255), 3)
        
        # English word
        cv2.putText(frame, english_word, (20, 80),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        # Movement description
        cv2.putText(frame, movement_desc[:50], (20, 110),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 2)
        
        # Progress
        cv2.putText(frame, f"Progress: {collected}/{total}", (20, 140),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # Hand detection status
        if hands_detected:
            cv2.putText(frame, "Hands: DETECTED", (20, 170),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        else:
            cv2.putText(frame, "Hands: NOT DETECTED", (20, 170),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        
        # Instructions
        cv2.putText(frame, "SPACE: Capture | ESC: Next Word | Q: Quit", (20, height-20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
    
    def collect_all_data(self, samples_per_word=50):
        """Collect data for all words"""
        print("🤖 نظام جمع البيانات للغة الإشارة العربية")
        print("="*70)
        print("الجملة المستهدفة:")
        print("'لو العالم كله سكت، هتعمل ايه؟")
        print(" هتكلم بايدي وهخلي العالم يسمعني'")
        print("="*70)
        print(f"📊 سيتم جمع {samples_per_word} عينة لكل من {len(self.word_pairs)} كلمة")
        print(f"📸 الصور ستُحفظ في: {self.images_dir}/")
        print(f"📊 البيانات ستُحفظ في: {self.landmarks_dir}/")
        print("="*70)
        
        all_data = []
        
        for i, (arabic_word, english_word, movement_desc) in enumerate(self.word_pairs, 1):
            print(f"\n🎯 الكلمة {i}/{len(self.word_pairs)}")
            
            choice = input(f"جمع بيانات '{arabic_word}'? (Enter=نعم, s=تخطي, q=خروج): ").lower().strip()
            
            if choice == 'q':
                break
            elif choice == 's':
                print(f"⏭️ تم تخطي '{arabic_word}'")
                continue
            
            success = self.collect_word_data(arabic_word, english_word, movement_desc, samples_per_word)
            if not success:
                break
            
            # Load collected data for this word
            word_filename = f"{english_word.replace(' ', '_')}_complete_data.json"
            word_filepath = os.path.join(self.data_dir, word_filename)
            if os.path.exists(word_filepath):
                with open(word_filepath, 'r', encoding='utf-8') as f:
                    word_data = json.load(f)
                    all_data.extend(word_data)
        
        # Save combined data
        if all_data:
            combined_filepath = os.path.join(self.data_dir, "all_arabic_signs_data.json")
            with open(combined_filepath, 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n🎉 تم الانتهاء من جمع البيانات!")
            print("="*70)
            print(f"📊 إجمالي العينات: {len(all_data)}")
            print(f"📸 الصور محفوظة في: {self.images_dir}/")
            print(f"📊 البيانات محفوظة في: {self.data_dir}/")
            print("\n💡 الخطوة التالية:")
            print("   قم بتشغيل نموذج التدريب لإنشاء نظام التعرف")

def main():
    """Main function"""
    collector = ArabicSignLanguageCollector()
    
    print("🤖 مرحباً بك في نظام جمع البيانات للغة الإشارة العربية")
    print("="*70)
    print("هذا النظام سيساعدك في جمع بيانات التدريب")
    print("للتعرف على لغة الإشارة العربية.")
    print("="*70)
    
    samples = input("عدد العينات لكل كلمة (افتراضي 50): ").strip()
    samples = int(samples) if samples.isdigit() else 50
    
    print(f"\n🚀 بدء جمع {samples} عينة لكل كلمة...")
    collector.collect_all_data(samples)
    
    print("\n👋 شكراً لاستخدام نظام جمع البيانات!")

if __name__ == "__main__":
    main()
