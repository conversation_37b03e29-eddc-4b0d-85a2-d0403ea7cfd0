#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Quick Training Script for Sign Language Recognition
سكريبت تدريب سريع لنظام التعرف على لغة الإشارة
"""

import json
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
import joblib
import os

class QuickSignLanguageTrainer:
    def __init__(self):
        self.model = None
        self.label_encoder = None
        self.model_dir = "models"
        
        if not os.path.exists(self.model_dir):
            os.makedirs(self.model_dir)
    
    def load_data(self, data_file="sign_data/all_signs_complete_data.json"):
        """Load training data"""
        print("📊 Loading training data...")
        
        if not os.path.exists(data_file):
            raise FileNotFoundError(f"Data file not found: {data_file}")
        
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        X = []
        y = []
        
        for sample in data:
            if sample.get('hand_detected', False):  # Only use samples with hands
                X.append(sample['landmarks'])
                y.append(sample['word_english'])
        
        print(f"✅ Loaded {len(X)} samples with hands detected")
        return np.array(X), np.array(y)
    
    def train_model(self, data_file="sign_data/all_signs_complete_data.json"):
        """Train the model quickly using Random Forest"""
        print("🚀 QUICK TRAINING - ARABIC SIGN LANGUAGE MODEL")
        print("="*60)
        
        # Load data
        X, y = self.load_data(data_file)
        
        # Check data distribution
        unique_words, counts = np.unique(y, return_counts=True)
        print(f"\n📋 Data distribution:")
        for word, count in zip(unique_words, counts):
            print(f"   {word}: {count} samples")
        
        # Encode labels
        print("\n🔤 Encoding labels...")
        self.label_encoder = LabelEncoder()
        y_encoded = self.label_encoder.fit_transform(y)
        
        # Split data
        print("📊 Splitting data...")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
        )
        
        print(f"   Training samples: {len(X_train)}")
        print(f"   Testing samples: {len(X_test)}")
        
        # Train Random Forest model (faster than neural network)
        print("\n🌳 Training Random Forest model...")
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=20,
            random_state=42,
            n_jobs=-1  # Use all CPU cores
        )
        
        self.model.fit(X_train, y_train)
        print("✅ Training completed!")
        
        # Evaluate model
        print("\n📈 Evaluating model...")
        y_pred = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"🎯 Test Accuracy: {accuracy:.4f} ({accuracy*100:.1f}%)")
        
        # Detailed classification report
        print("\n📊 Detailed Results:")
        target_names = self.label_encoder.classes_
        report = classification_report(y_test, y_pred, target_names=target_names)
        print(report)
        
        return accuracy
    
    def save_model(self):
        """Save the trained model"""
        print("\n💾 Saving model...")
        
        # Save Random Forest model
        model_path = os.path.join(self.model_dir, "quick_sign_model.pkl")
        joblib.dump(self.model, model_path)
        
        # Save label encoder
        encoder_path = os.path.join(self.model_dir, "quick_label_encoder.pkl")
        joblib.dump(self.label_encoder, encoder_path)
        
        print(f"✅ Model saved to: {model_path}")
        print(f"✅ Encoder saved to: {encoder_path}")
    
    def load_model(self):
        """Load the trained model"""
        model_path = os.path.join(self.model_dir, "quick_sign_model.pkl")
        encoder_path = os.path.join(self.model_dir, "quick_label_encoder.pkl")
        
        if os.path.exists(model_path) and os.path.exists(encoder_path):
            self.model = joblib.load(model_path)
            self.label_encoder = joblib.load(encoder_path)
            print("✅ Model loaded successfully")
            return True
        else:
            print("❌ Model files not found")
            return False
    
    def predict(self, landmarks):
        """Make prediction on new landmarks"""
        if self.model is None or self.label_encoder is None:
            print("❌ Model not loaded")
            return None, 0.0
        
        landmarks = np.array(landmarks).reshape(1, -1)
        
        # Get prediction and probability
        prediction = self.model.predict(landmarks)[0]
        probabilities = self.model.predict_proba(landmarks)[0]
        confidence = probabilities[prediction]
        
        # Convert back to word
        predicted_word = self.label_encoder.inverse_transform([prediction])[0]
        
        return predicted_word, confidence
    
    def test_predictions(self):
        """Test the model with some sample predictions"""
        if not self.load_model():
            print("❌ Cannot test - model not found")
            return
        
        print("\n🧪 Testing model predictions...")
        
        # Load test data
        try:
            X, y = self.load_data()
            
            # Test on a few random samples
            test_indices = np.random.choice(len(X), min(10, len(X)), replace=False)
            
            print("\n📋 Sample predictions:")
            print("-" * 60)
            
            correct = 0
            for i, idx in enumerate(test_indices):
                true_word = y[idx]
                predicted_word, confidence = self.predict(X[idx])
                
                is_correct = "✅" if predicted_word == true_word else "❌"
                print(f"{i+1:2d}. True: {true_word}")
                print(f"    Pred: {predicted_word} (confidence: {confidence:.3f}) {is_correct}")
                
                if predicted_word == true_word:
                    correct += 1
            
            accuracy = correct / len(test_indices)
            print(f"\n🎯 Sample accuracy: {accuracy:.3f} ({accuracy*100:.1f}%)")
            
        except Exception as e:
            print(f"❌ Error testing predictions: {e}")

def main():
    """Main training function"""
    trainer = QuickSignLanguageTrainer()
    
    print("🤖 QUICK SIGN LANGUAGE MODEL TRAINER")
    print("="*60)
    print("This will train a fast Random Forest model")
    print("for Arabic sign language recognition.")
    print("="*60)
    
    try:
        # Train the model
        accuracy = trainer.train_model()
        
        # Save the model
        trainer.save_model()
        
        # Test predictions
        trainer.test_predictions()
        
        print("\n🎉 TRAINING COMPLETE!")
        print("="*60)
        print(f"✅ Model accuracy: {accuracy*100:.1f}%")
        print("✅ Model saved successfully")
        print("✅ Ready for use in the application")
        print("\n💡 Next steps:")
        print("   1. Run 'python app.py' to start the web application")
        print("   2. Test the model with real hand gestures")
        print("   3. The model will recognize the 12 Arabic sign words")
        
    except FileNotFoundError:
        print("❌ Training data not found!")
        print("   Please run data collection first:")
        print("   python generate_sample_data.py")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")

if __name__ == "__main__":
    main()
