from flask import Flask, render_template, Response, jsonify
import cv2
import mediapipe as mp
import numpy as np
import json
from model import SignLanguageModel
import threading
import time

app = Flask(__name__)

class SignLanguageApp:
    def __init__(self):
        self.model = SignLanguageModel()
        self.model.load_model()
        
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        self.current_prediction = ""
        self.current_confidence = 0.0
        self.is_hands_detected = False
        
        # للجملة المكتملة
        self.sentence = []
        self.last_prediction = ""
        self.prediction_count = 0
        self.required_count = 10  # عدد التنبؤات المتتالية المطلوبة
        
    def extract_landmarks(self, results):
        """استخراج النقاط المميزة من اليدين"""
        landmarks = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                hand_points = []
                for landmark in hand_landmarks.landmark:
                    hand_points.extend([landmark.x, landmark.y, landmark.z])
                landmarks.extend(hand_points)
        
        while len(landmarks) < 126:
            landmarks.append(0.0)
            
        return landmarks[:126]
    
    def generate_frames(self):
        """توليد الإطارات للبث المباشر"""
        cap = cv2.VideoCapture(0)
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            # رسم النقاط المميزة
            if results.multi_hand_landmarks:
                self.is_hands_detected = True
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS,
                        self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2),
                        self.mp_drawing.DrawingSpec(color=(255, 0, 0), thickness=2)
                    )
                
                # التنبؤ
                landmarks = self.extract_landmarks(results)
                predicted_word, confidence = self.model.predict_sign(landmarks)
                
                if confidence > 0.8:  # عتبة الثقة
                    self.current_prediction = predicted_word
                    self.current_confidence = confidence
                    
                    # إضافة للجملة إذا كان التنبؤ مستقر
                    if predicted_word == self.last_prediction:
                        self.prediction_count += 1
                    else:
                        self.prediction_count = 1
                        self.last_prediction = predicted_word
                    
                    if self.prediction_count >= self.required_count:
                        if predicted_word not in self.sentence:
                            self.sentence.append(predicted_word)
                        self.prediction_count = 0
                else:
                    self.current_prediction = ""
                    self.current_confidence = 0.0
            else:
                self.is_hands_detected = False
                self.current_prediction = ""
                self.current_confidence = 0.0
            
            # إضافة معلومات على الإطار
            self.add_info_to_frame(frame)
            
            # تحويل الإطار إلى JPEG
            ret, buffer = cv2.imencode('.jpg', frame)
            frame = buffer.tobytes()
            
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
    
    def add_info_to_frame(self, frame):
        """إضافة المعلومات على الإطار"""
        height, width = frame.shape[:2]
        
        # خلفية شفافة للنص
        overlay = frame.copy()
        
        # منطقة المعلومات
        cv2.rectangle(overlay, (10, 10), (width-10, 150), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # النص الحالي
        if self.is_hands_detected:
            if self.current_prediction:
                cv2.putText(frame, f"الكلمة: {self.current_prediction}", (20, 40),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                cv2.putText(frame, f"الثقة: {self.current_confidence:.2f}", (20, 70),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            else:
                cv2.putText(frame, "غير واضح", (20, 40),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
        else:
            cv2.putText(frame, "ضع يديك أمام الكاميرا", (20, 40),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        # الجملة المكونة
        if self.sentence:
            sentence_text = " ".join(self.sentence)
            cv2.putText(frame, f"الجملة: {sentence_text}", (20, 110),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # تعليمات
        cv2.putText(frame, "اضغط R لمسح الجملة", (20, 130),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

sign_app = SignLanguageApp()

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/video_feed')
def video_feed():
    """بث الفيديو المباشر"""
    return Response(sign_app.generate_frames(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/get_prediction')
def get_prediction():
    """الحصول على التنبؤ الحالي"""
    return jsonify({
        'prediction': sign_app.current_prediction,
        'confidence': sign_app.current_confidence,
        'hands_detected': sign_app.is_hands_detected,
        'sentence': sign_app.sentence
    })

@app.route('/clear_sentence')
def clear_sentence():
    """مسح الجملة"""
    sign_app.sentence = []
    return jsonify({'status': 'success'})

@app.route('/add_word/<word>')
def add_word(word):
    """إضافة كلمة للجملة يدوياً"""
    if word not in sign_app.sentence:
        sign_app.sentence.append(word)
    return jsonify({'status': 'success', 'sentence': sign_app.sentence})

if __name__ == '__main__':
    print("تشغيل تطبيق التعرف على لغة الإشارة...")
    print("افتح المتصفح على: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
