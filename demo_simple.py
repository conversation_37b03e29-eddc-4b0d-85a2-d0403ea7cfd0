#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple Arabic Sign Language Demo
عرض توضيحي بسيط لنظام التعرف على لغة الإشارة العربية
"""

import cv2
import mediapipe as mp
import numpy as np
import time
import os

class SimpleArabicSignDemo:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Word pairs: (Arabic, English, Number)
        self.target_words = [
            ("لو", "Law", "1"),
            ("العالم", "Al-alam", "2"),
            ("كله", "Kullu", "3"),
            ("سكت", "Sakat", "4"),
            ("هتعم<PERSON>", "<PERSON><PERSON>amal", "5"),
            ("ايه", "Eh", "6"),
            ("هتكلم", "<PERSON><PERSON><PERSON>", "7"),
            ("بايدي", "Bi-yadi", "8"),
            ("وهخلي", "Wa-hakhalli", "9"),
            ("العالم", "Al-alam", "10"),
            ("يسمعني", "Yasma'ni", "11")
        ]
        
        # Simulation variables
        self.current_word_index = 0
        self.last_change_time = time.time()
        self.change_interval = 4  # Change every 4 seconds
        
    def clear_screen(self):
        """Clear terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def extract_landmarks(self, results):
        """Extract hand landmarks"""
        landmarks = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                hand_points = []
                for landmark in hand_landmarks.landmark:
                    hand_points.extend([landmark.x, landmark.y, landmark.z])
                landmarks.extend(hand_points)
        
        while len(landmarks) < 126:
            landmarks.append(0.0)
            
        return landmarks[:126]
    
    def simulate_prediction(self, landmarks):
        """Simulate prediction for target sentence"""
        current_time = time.time()
        
        # Change word every interval
        if current_time - self.last_change_time > self.change_interval:
            if self.current_word_index < len(self.target_words) - 1:
                self.current_word_index += 1
            else:
                self.current_word_index = 0  # Reset to beginning
            self.last_change_time = current_time
            
            # Print current word in terminal
            arabic_word, english_word, number = self.target_words[self.current_word_index]
            print(f"\n🎯 Current Word #{number}: {arabic_word} ({english_word})")
        
        # If hands detected, give high confidence
        if len([x for x in landmarks if x != 0.0]) > 10:
            confidence = 0.85 + np.random.random() * 0.1
            arabic_word, english_word, number = self.target_words[self.current_word_index]
            return arabic_word, english_word, number, confidence
        else:
            return "", "", "", 0.0
    
    def run_demo(self):
        """Run the demo"""
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ Cannot access camera")
            print("   Make sure camera is connected and not used by other apps")
            return
        
        print("🎥 Starting Arabic Sign Language Demo...")
        print("="*60)
        print("🎯 Target Sentence:")
        print("Arabic: لو العالم كله سكت، هتعمل ايه؟ هتكلم بايدي وهخلي العالم يسمعني")
        print("English: Law al-alam kullu sakat, hat'amal eh?")
        print("         Hatkallam bi-yadi wa-hakhalli al-alam yasma'ni")
        print("="*60)
        print("Instructions:")
        print("- Words change automatically every 4 seconds")
        print("- Press SPACE to manually add current word to sentence")
        print("- Press R to clear sentence")
        print("- Press ESC to exit")
        print("="*60)
        
        sentence_ar = []
        sentence_en = []
        last_prediction = ""
        prediction_count = 0
        required_count = 8  # Required consecutive predictions
        
        # Print first word
        arabic_word, english_word, number = self.target_words[0]
        print(f"\n🎯 Current Word #{number}: {arabic_word} ({english_word})")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            # Draw hand landmarks
            hands_detected = False
            if results.multi_hand_landmarks:
                hands_detected = True
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS,
                        self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2),
                        self.mp_drawing.DrawingSpec(color=(255, 0, 0), thickness=2)
                    )
            
            # Extract landmarks and predict
            landmarks = self.extract_landmarks(results)
            predicted_word_ar, predicted_word_en, word_number, confidence = self.simulate_prediction(landmarks)
            
            # Add info to frame (English only to avoid encoding issues)
            self.add_info_to_frame(frame, predicted_word_en, word_number,
                                 confidence, hands_detected, sentence_en)
            
            # Add to sentence if prediction is stable
            if predicted_word_ar and confidence > 0.8:
                if predicted_word_ar == last_prediction:
                    prediction_count += 1
                else:
                    prediction_count = 1
                    last_prediction = predicted_word_ar
                
                if prediction_count >= required_count:
                    if predicted_word_ar not in sentence_ar:
                        sentence_ar.append(predicted_word_ar)
                        sentence_en.append(predicted_word_en)
                        print(f"✅ Added to sentence: {predicted_word_ar} ({predicted_word_en})")
                        print(f"📝 Current sentence: {' '.join(sentence_ar)}")
                    prediction_count = 0
            
            cv2.imshow('Arabic Sign Language Demo - Camera View', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == 27:  # ESC
                break
            elif key == ord('r') or key == ord('R'):  # Clear sentence
                sentence_ar = []
                sentence_en = []
                print("🗑️ Sentence cleared")
            elif key == ord(' '):  # SPACE - manually add current word
                if predicted_word_ar and predicted_word_ar not in sentence_ar:
                    sentence_ar.append(predicted_word_ar)
                    sentence_en.append(predicted_word_en)
                    print(f"➕ Manually added: {predicted_word_ar} ({predicted_word_en})")
                    print(f"📝 Current sentence: {' '.join(sentence_ar)}")
        
        cap.release()
        cv2.destroyAllWindows()
        
        # Final results
        print("\n" + "="*60)
        print("🎉 Demo Finished!")
        print("="*60)
        
        if sentence_ar:
            print(f"📝 Final sentence (Arabic): {' '.join(sentence_ar)}")
            print(f"📝 Final sentence (English): {' '.join(sentence_en)}")
            
            # Check if complete
            if len(sentence_ar) == len(self.target_words):
                print("🏆 Congratulations! You completed the full sentence!")
            else:
                progress = len(sentence_ar) / len(self.target_words) * 100
                print(f"📊 Progress: {len(sentence_ar)}/{len(self.target_words)} words ({progress:.1f}%)")
        else:
            print("📝 No words were added to the sentence")
        
        print("👋 Thank you for using the Arabic Sign Language Demo!")
    
    def add_info_to_frame(self, frame, prediction_en, word_number,
                         confidence, hands_detected, sentence_en):
        """Add information to frame (English text only)"""
        height, width = frame.shape[:2]
        
        # Semi-transparent background for text
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (width-10, 200), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Hand status
        if hands_detected:
            status_text = "✅ Hands Detected"
            status_color = (0, 255, 0)
        else:
            status_text = "❌ Place hands in front of camera"
            status_color = (0, 0, 255)
        
        cv2.putText(frame, status_text, (20, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        
        # Current prediction
        if prediction_en and confidence > 0.7:
            cv2.putText(frame, f"Word #{word_number}: {prediction_en}", (20, 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(frame, f"Confidence: {confidence:.0%}", (20, 110),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        else:
            cv2.putText(frame, "Waiting for sign...", (20, 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
        
        # Current sentence progress
        progress = len(sentence_en)
        total = len(self.target_words)
        cv2.putText(frame, f"Progress: {progress}/{total} words", (20, 140),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # Instructions
        cv2.putText(frame, "SPACE: Add word | R: Clear | ESC: Exit", (20, 170),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

def main():
    """Main function"""
    print("🤖 Arabic Sign Language Recognition Demo")
    print("   Simple Version with Terminal Arabic Display")
    print("="*60)
    
    try:
        demo = SimpleArabicSignDemo()
        demo.run_demo()
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Install required packages:")
        print("   pip install opencv-python mediapipe")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
