#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple Sign Language Model (No ML Libraries)
نموذج بسيط للتعرف على لغة الإشارة (بدون مكتبات تعلم آلي)
"""

import json
import os
import numpy as np
import random
from datetime import datetime

class SimpleSignLanguageModel:
    def __init__(self):
        self.word_patterns = {}
        self.words = [
            "As-salamu alaykum", "Wa alaykumu s-salamu", "Law", "Al-alam",
            "Kullu", "Sakat", "<PERSON>'amal", "<PERSON>h", "<PERSON><PERSON><PERSON>", 
            "<PERSON><PERSON>-ya<PERSON>", "Wa-hakha<PERSON>", "Yasma'ni"
        ]
        self.model_dir = "models"
        
        if not os.path.exists(self.model_dir):
            os.makedirs(self.model_dir)
    
    def load_data(self, data_file="sign_data/all_signs_complete_data.json"):
        """Load and process training data"""
        print("📊 Loading training data...")
        
        if not os.path.exists(data_file):
            print(f"❌ Data file not found: {data_file}")
            return False
        
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"✅ Loaded {len(data)} samples")
            
            # Group data by word
            word_data = {}
            for sample in data:
                if sample.get('hand_detected', False):
                    word = sample['word_english']
                    if word not in word_data:
                        word_data[word] = []
                    word_data[word].append(sample['landmarks'])
            
            # Calculate average patterns for each word
            for word, landmarks_list in word_data.items():
                if landmarks_list:
                    # Calculate average landmarks
                    avg_landmarks = np.mean(landmarks_list, axis=0)
                    self.word_patterns[word] = avg_landmarks.tolist()
                    print(f"   {word}: {len(landmarks_list)} samples")
            
            print(f"✅ Processed {len(self.word_patterns)} words")
            return True
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return False
    
    def calculate_similarity(self, landmarks1, landmarks2):
        """Calculate similarity between two landmark sets"""
        try:
            # Convert to numpy arrays
            l1 = np.array(landmarks1)
            l2 = np.array(landmarks2)
            
            # Calculate Euclidean distance
            distance = np.sqrt(np.sum((l1 - l2) ** 2))
            
            # Convert to similarity (0-1, higher is more similar)
            max_distance = np.sqrt(len(landmarks1))  # Theoretical max distance
            similarity = max(0, 1 - (distance / max_distance))
            
            return similarity
            
        except Exception as e:
            print(f"Error calculating similarity: {e}")
            return 0.0
    
    def predict(self, landmarks):
        """Predict word from landmarks"""
        if not self.word_patterns:
            return "Unknown", 0.0
        
        best_word = "Unknown"
        best_similarity = 0.0
        
        # Compare with all word patterns
        for word, pattern in self.word_patterns.items():
            similarity = self.calculate_similarity(landmarks, pattern)
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_word = word
        
        return best_word, best_similarity
    
    def train_model(self):
        """Train the simple model"""
        print("🚀 SIMPLE SIGN LANGUAGE MODEL TRAINING")
        print("="*50)
        
        # Load data
        if not self.load_data():
            return False
        
        # Save the model (word patterns)
        self.save_model()
        
        # Test the model
        self.test_model()
        
        return True
    
    def save_model(self):
        """Save the trained model"""
        model_file = os.path.join(self.model_dir, "simple_sign_model.json")
        
        model_data = {
            'word_patterns': self.word_patterns,
            'words': self.words,
            'trained_at': datetime.now().isoformat(),
            'model_type': 'simple_pattern_matching'
        }
        
        try:
            with open(model_file, 'w', encoding='utf-8') as f:
                json.dump(model_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Model saved to: {model_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving model: {e}")
            return False
    
    def load_model(self):
        """Load the trained model"""
        model_file = os.path.join(self.model_dir, "simple_sign_model.json")
        
        if not os.path.exists(model_file):
            print("❌ Model file not found")
            return False
        
        try:
            with open(model_file, 'r', encoding='utf-8') as f:
                model_data = json.load(f)
            
            self.word_patterns = model_data['word_patterns']
            self.words = model_data.get('words', self.words)
            
            print("✅ Model loaded successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False
    
    def test_model(self):
        """Test the model with training data"""
        print("\n🧪 Testing model...")
        
        # Load test data
        try:
            with open("sign_data/all_signs_complete_data.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Test on random samples
            test_samples = random.sample([s for s in data if s.get('hand_detected', False)], 
                                       min(20, len(data)))
            
            correct = 0
            total = len(test_samples)
            
            print("\n📋 Sample predictions:")
            print("-" * 60)
            
            for i, sample in enumerate(test_samples):
                true_word = sample['word_english']
                predicted_word, confidence = self.predict(sample['landmarks'])
                
                is_correct = "✅" if predicted_word == true_word else "❌"
                print(f"{i+1:2d}. True: {true_word}")
                print(f"    Pred: {predicted_word} (confidence: {confidence:.3f}) {is_correct}")
                
                if predicted_word == true_word:
                    correct += 1
            
            accuracy = correct / total if total > 0 else 0
            print(f"\n🎯 Test Accuracy: {accuracy:.3f} ({accuracy*100:.1f}%)")
            
            return accuracy
            
        except Exception as e:
            print(f"❌ Error testing model: {e}")
            return 0.0
    
    def get_model_info(self):
        """Get information about the trained model"""
        if not self.word_patterns:
            return "Model not trained"
        
        info = []
        info.append(f"📊 Model Information:")
        info.append(f"   Type: Simple Pattern Matching")
        info.append(f"   Words: {len(self.word_patterns)}")
        info.append(f"   Supported words:")
        
        for word in sorted(self.word_patterns.keys()):
            info.append(f"     - {word}")
        
        return "\n".join(info)

def main():
    """Main function"""
    model = SimpleSignLanguageModel()
    
    print("🤖 SIMPLE SIGN LANGUAGE MODEL")
    print("="*50)
    print("This creates a simple pattern-matching model")
    print("for Arabic sign language recognition.")
    print("="*50)
    
    # Train the model
    if model.train_model():
        print("\n🎉 TRAINING COMPLETE!")
        print("="*50)
        print("✅ Simple model trained successfully")
        print("✅ Model saved and ready for use")
        print("\n" + model.get_model_info())
        print("\n💡 Next steps:")
        print("   1. Run 'python app.py' to start the web application")
        print("   2. The model will use pattern matching for recognition")
        print("   3. Test with real hand gestures")
    else:
        print("❌ Training failed!")
        print("   Make sure data is available in sign_data/")

if __name__ == "__main__":
    main()
