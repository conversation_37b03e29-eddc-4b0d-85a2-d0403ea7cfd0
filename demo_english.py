#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
English Demo - Arabic Sign Language Recognition
عرض توضيحي - نظام التعرف على لغة الإشارة العربية
"""

import cv2
import mediapipe as mp
import numpy as np
import time

class ArabicSignDemo:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Arabic words (English transliteration)
        self.words = [
            "As-salamu alaykum",
            "Wa alaykumu s-salamu wa-rahmatu llahi wa-barakatuh",
            "Law", "Al-alam", "Kullu", "Sakat",
            "Hat'amal", "Eh", "Hatkallam", "Bi-ya<PERSON>",
            "<PERSON>a<PERSON><PERSON><PERSON><PERSON>", "Ya<PERSON>'ni"
        ]
        
        # Arabic words (original)
        self.arabic_words = [
            "السلام عليكم",
            "وعليكم السلام ورحمة الله وبركاته",
            "لو", "العالم", "كله", "سكت",
            "هتعمل", "ايه", "هتكلم", "بايدي",
            "وهخلي", "يسمعني"
        ]
        
        # Simulation variables
        self.current_word_index = 0
        self.last_change_time = time.time()
        self.change_interval = 3  # Change every 3 seconds
        
    def extract_landmarks(self, results):
        """Extract hand landmarks"""
        landmarks = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                hand_points = []
                for landmark in hand_landmarks.landmark:
                    hand_points.extend([landmark.x, landmark.y, landmark.z])
                landmarks.extend(hand_points)
        
        while len(landmarks) < 126:  # 21 points × 3 coordinates × 2 hands
            landmarks.append(0.0)
            
        return landmarks[:126]
    
    def simulate_prediction(self, landmarks):
        """Simulate prediction"""
        current_time = time.time()
        
        # Change word every interval
        if current_time - self.last_change_time > self.change_interval:
            self.current_word_index = (self.current_word_index + 1) % len(self.words)
            self.last_change_time = current_time
        
        # If hands detected, give high confidence
        if len([x for x in landmarks if x != 0.0]) > 10:
            confidence = 0.85 + np.random.random() * 0.1  # 85-95% confidence
            return (self.words[self.current_word_index], 
                   self.arabic_words[self.current_word_index], 
                   confidence)
        else:
            return "", "", 0.0
    
    def run_demo(self):
        """Run the demo"""
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ Cannot access camera")
            print("   Make sure camera is connected and not used by other apps")
            return
        
        print("🎥 Starting Arabic Sign Language Demo...")
        print("   Press ESC to exit")
        print("   Press R to clear sentence")
        
        sentence_en = []
        sentence_ar = []
        last_prediction = ""
        prediction_count = 0
        required_count = 15  # Required consecutive predictions
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            # Draw hand landmarks
            hands_detected = False
            if results.multi_hand_landmarks:
                hands_detected = True
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS,
                        self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2),
                        self.mp_drawing.DrawingSpec(color=(255, 0, 0), thickness=2)
                    )
            
            # Extract landmarks and predict
            landmarks = self.extract_landmarks(results)
            predicted_word_en, predicted_word_ar, confidence = self.simulate_prediction(landmarks)
            
            # Add info to frame
            self.add_info_to_frame(frame, predicted_word_en, predicted_word_ar, 
                                 confidence, hands_detected, sentence_en)
            
            # Add to sentence if prediction is stable
            if predicted_word_en and confidence > 0.8:
                if predicted_word_en == last_prediction:
                    prediction_count += 1
                else:
                    prediction_count = 1
                    last_prediction = predicted_word_en
                
                if prediction_count >= required_count:
                    if predicted_word_en not in sentence_en:
                        sentence_en.append(predicted_word_en)
                        sentence_ar.append(predicted_word_ar)
                        print(f"✅ Added: {predicted_word_en}")
                    prediction_count = 0
            
            cv2.imshow('Arabic Sign Language Demo', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == 27:  # ESC
                break
            elif key == ord('r') or key == ord('R'):  # Clear sentence
                sentence_en = []
                sentence_ar = []
                print("🗑️ Sentence cleared")
        
        cap.release()
        cv2.destroyAllWindows()
        
        if sentence_en:
            print(f"\n📝 Final sentence (English): {' '.join(sentence_en)}")
            print(f"📝 Final sentence (Arabic): {' '.join(sentence_ar)}")
        
        print("👋 Demo finished")
    
    def add_info_to_frame(self, frame, prediction_en, prediction_ar, 
                         confidence, hands_detected, sentence):
        """Add information to frame"""
        height, width = frame.shape[:2]
        
        # Semi-transparent background for text
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (width-10, 220), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Hand status
        if hands_detected:
            status_text = "✅ Hands Detected"
            status_color = (0, 255, 0)
        else:
            status_text = "❌ Place hands in front of camera"
            status_color = (0, 0, 255)
        
        cv2.putText(frame, status_text, (20, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        
        # Current prediction
        if prediction_en and confidence > 0.7:
            cv2.putText(frame, f"Word: {prediction_en}", (20, 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(frame, f"Confidence: {confidence:.0%}", (20, 110),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        else:
            cv2.putText(frame, "Waiting for sign...", (20, 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
        
        # Current sentence
        if sentence:
            sentence_text = " ".join(sentence)
            # Split text if too long
            if len(sentence_text) > 50:
                sentence_text = sentence_text[:47] + "..."
            cv2.putText(frame, f"Sentence: {sentence_text}", (20, 150),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        else:
            cv2.putText(frame, "Sentence: (empty)", (20, 150),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (128, 128, 128), 2)
        
        # Instructions
        cv2.putText(frame, "ESC: Exit | R: Clear sentence", (20, 190),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Target sentence
        cv2.putText(frame, "Target: Law al-alam kullu sakat...", (20, 210),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)

def main():
    """Main function"""
    print("🤖 Arabic Sign Language Recognition Demo")
    print("="*50)
    print("Supported words:")
    print("1. As-salamu alaykum")
    print("2. Wa alaykumu s-salamu wa-rahmatu llahi wa-barakatuh")
    print("3. Law, Al-alam, Kullu, Sakat")
    print("4. Hat'amal, Eh, Hatkallam, Bi-yadi")
    print("5. Wa-hakhalli, Yasma'ni")
    print()
    print("Target sentence:")
    print("'Law al-alam kullu sakat, hat'amal eh?")
    print(" Hatkallam bi-yadi wa-hakhalli al-alam yasma'ni'")
    print("="*50)
    
    try:
        demo = ArabicSignDemo()
        demo.run_demo()
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure to install: pip install opencv-python mediapipe")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
