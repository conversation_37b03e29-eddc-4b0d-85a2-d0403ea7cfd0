import cv2
import mediapipe as mp
import numpy as np
import os
import json
import time
from datetime import datetime

class SignLanguageDataCollectorWithImages:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Word pairs: (Arabic, English, Movement Description)
        self.word_pairs = [
            ("السلام عليكم", "As-salamu alaykum", "Right hand open, move left to right + left hand on heart"),
            ("وعليكم السلام ورحمة الله وبركاته", "Wa alaykumu s-salamu", "Both hands open, circular motion up and out"),
            ("لو", "Law", "Index finger up, move left-right (questioning)"),
            ("العالم", "Al-alam", "Hands in ball shape, circular motion around each other"),
            ("كله", "Kullu", "Open hands wide outward, then bring together"),
            ("سكت", "Sakat", "Index finger on lips (silence gesture)"),
            ("هتعمل", "Hat'amal", "Two fists moving back and forth (working motion)"),
            ("ايه", "Eh", "Raise hands up with open fingers (questioning)"),
            ("هتكلم", "Hatkallam", "Hand in front of mouth, move fingers (talking)"),
            ("بايدي", "Bi-yadi", "Raise both hands with open palms"),
            ("وهخلي", "Wa-hakhalli", "From fist to open hand forward (giving/making)"),
            ("يسمعني", "Yasma'ni", "Hand behind ear then forward (listening)")
        ]
        
        # Create directories
        self.data_dir = "sign_data"
        self.images_dir = "sign_images"
        self.landmarks_dir = "landmarks_data"
        
        for directory in [self.data_dir, self.images_dir, self.landmarks_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 Created directory: {directory}")
    
    def extract_landmarks(self, results):
        """Extract landmarks from hands"""
        landmarks = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                hand_points = []
                for landmark in hand_landmarks.landmark:
                    hand_points.extend([landmark.x, landmark.y, landmark.z])
                landmarks.extend(hand_points)
        
        # Fill with zeros if less than 2 hands detected
        while len(landmarks) < 126:  # 21 points × 3 coordinates × 2 hands
            landmarks.append(0.0)
            
        return landmarks[:126]  # Ensure fixed size
    
    def save_sample(self, frame, landmarks, arabic_word, english_word, sample_id):
        """Save both image and landmark data"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        
        # Save original image
        img_filename = f"{english_word.replace(' ', '_')}_{sample_id:03d}_{timestamp}.jpg"
        img_path = os.path.join(self.images_dir, img_filename)
        cv2.imwrite(img_path, frame)
        
        # Save landmarks data
        landmark_data = {
            'word_arabic': arabic_word,
            'word_english': english_word,
            'sample_id': sample_id,
            'timestamp': timestamp,
            'image_filename': img_filename,
            'landmarks': landmarks,
            'hand_detected': len([x for x in landmarks if x != 0.0]) > 10
        }
        
        landmark_filename = f"{english_word.replace(' ', '_')}_{sample_id:03d}_{timestamp}.json"
        landmark_path = os.path.join(self.landmarks_dir, landmark_filename)
        
        with open(landmark_path, 'w', encoding='utf-8') as f:
            json.dump(landmark_data, f, ensure_ascii=False, indent=2)
        
        return img_path, landmark_path
    
    def collect_word_data(self, arabic_word, english_word, movement_desc, samples_per_word=30):
        """Collect data for a specific word"""
        cap = cv2.VideoCapture(0)
        word_data = []
        sample_count = 0
        
        print(f"\n{'='*70}")
        print(f"📊 COLLECTING DATA FOR WORD #{sample_count+1}")
        print(f"{'='*70}")
        print(f"Arabic Word: {arabic_word}")
        print(f"English: {english_word}")
        print(f"Movement: {movement_desc}")
        print(f"Target: {samples_per_word} samples")
        print(f"{'='*70}")
        print("INSTRUCTIONS:")
        print("- Make sure you have good lighting")
        print("- Place hands clearly in front of camera")
        print("- Press SPACE to capture image + landmarks")
        print("- Press ESC to skip this word")
        print("- Make the movement clear and distinctive")
        print("- Hold the pose for a moment before pressing SPACE")
        print(f"{'='*70}")
        
        input("Press Enter when ready to start...")
        
        while sample_count < samples_per_word:
            ret, frame = cap.read()
            if not ret:
                break
                
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            # Draw hand landmarks
            hands_detected = False
            if results.multi_hand_landmarks:
                hands_detected = True
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS,
                        self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2),
                        self.mp_drawing.DrawingSpec(color=(255, 0, 0), thickness=2)
                    )
            
            # Add info to frame
            cv2.putText(frame, f"Word: {english_word}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Samples: {sample_count}/{samples_per_word}", 
                       (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # Hand detection status
            if hands_detected:
                cv2.putText(frame, "✅ Hands Detected", (10, 110), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            else:
                cv2.putText(frame, "❌ No Hands Detected", (10, 110), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            cv2.putText(frame, "SPACE: Capture | ESC: Skip", 
                       (10, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"Movement: {movement_desc[:45]}", 
                       (10, 170), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            
            cv2.imshow('Sign Language Data Collection - Press SPACE to capture', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord(' '):  # SPACE to capture
                if hands_detected:
                    # Extract landmarks
                    landmarks = self.extract_landmarks(results)
                    
                    # Save image and landmarks
                    img_path, landmark_path = self.save_sample(
                        frame, landmarks, arabic_word, english_word, sample_count + 1
                    )
                    
                    # Add to word data
                    word_data.append({
                        'word_arabic': arabic_word,
                        'word_english': english_word,
                        'sample_id': sample_count + 1,
                        'landmarks': landmarks,
                        'image_path': img_path,
                        'landmark_path': landmark_path,
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    sample_count += 1
                    print(f"✅ Sample {sample_count} captured for '{english_word}'")
                    print(f"   📸 Image saved: {os.path.basename(img_path)}")
                    print(f"   📊 Landmarks saved: {os.path.basename(landmark_path)}")
                    
                    # Brief pause to avoid accidental double captures
                    time.sleep(0.5)
                else:
                    print("❌ No hands detected! Please show your hands clearly")
                    
            elif key == 27:  # ESC to skip
                print(f"⏭️ Skipped '{english_word}' with {sample_count} samples")
                break
        
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"✅ Completed '{english_word}' with {sample_count} samples")
        return word_data
    
    def save_combined_data(self, all_data, filename):
        """Save combined data to JSON file"""
        filepath = os.path.join(self.data_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=2)
        print(f"💾 Combined data saved to: {filepath}")
    
    def collect_all_data(self, samples_per_word=30):
        """Collect data for all words"""
        all_data = []
        
        print("🤖 ARABIC SIGN LANGUAGE DATA COLLECTION WITH IMAGES")
        print("="*70)
        print("TARGET SENTENCE:")
        print("'Law al-alam kullu sakat, hat'amal eh?")
        print(" Hatkallam bi-yadi wa-hakhalli al-alam yasma'ni'")
        print("="*70)
        print(f"📊 COLLECTING {samples_per_word} SAMPLES FOR EACH OF {len(self.word_pairs)} WORDS")
        print(f"📸 Images will be saved in: {self.images_dir}/")
        print(f"📊 Landmarks will be saved in: {self.landmarks_dir}/")
        print("="*70)
        
        for i, (arabic_word, english_word, movement_desc) in enumerate(self.word_pairs, 1):
            print(f"\n🎯 WORD {i}/{len(self.word_pairs)}")
            print(f"Next: {english_word} ({arabic_word})")
            print(f"Movement: {movement_desc}")
            
            choice = input("Press Enter to continue, 's' to skip, 'q' to quit: ").lower().strip()
            if choice == 'q':
                print("🛑 Collection stopped by user")
                break
            elif choice == 's':
                print(f"⏭️ Skipped {english_word}")
                continue
            
            word_data = self.collect_word_data(arabic_word, english_word, movement_desc, samples_per_word)
            all_data.extend(word_data)
            
            # Save individual word data
            word_filename = f"{english_word.replace(' ', '_').replace('-', '_')}_complete_data.json"
            self.save_combined_data(word_data, word_filename)
        
        # Save all data combined
        if all_data:
            self.save_combined_data(all_data, "all_signs_complete_data.json")
            
            # Generate summary
            self.generate_summary(all_data)
        else:
            print("❌ No data collected")
    
    def generate_summary(self, all_data):
        """Generate collection summary"""
        print(f"\n🎉 COLLECTION COMPLETE!")
        print("="*70)
        print(f"📊 Total samples collected: {len(all_data)}")
        print(f"📸 Images saved in: {self.images_dir}/")
        print(f"📊 Landmarks saved in: {self.landmarks_dir}/")
        print(f"📁 Combined data saved in: {self.data_dir}/")
        
        # Count samples per word
        word_counts = {}
        for sample in all_data:
            word = sample['word_english']
            word_counts[word] = word_counts.get(word, 0) + 1
        
        print("\n📋 Samples per word:")
        for word, count in word_counts.items():
            print(f"   {word}: {count} samples")
        
        print("\n💡 Next steps:")
        print("1. Review the collected images in the 'sign_images' folder")
        print("2. Check landmark data in the 'landmarks_data' folder")
        print("3. Run 'python train_model.py' to train the model")
        print("4. The system will use both images and landmarks for training")

    def show_word_list(self):
        """Show list of words to collect"""
        print("\n📋 WORDS TO COLLECT:")
        print("="*70)
        for i, (arabic, english, movement) in enumerate(self.word_pairs, 1):
            print(f"{i:2d}. {english}")
            print(f"    Arabic: {arabic}")
            print(f"    Movement: {movement}")
            print()

if __name__ == "__main__":
    collector = SignLanguageDataCollectorWithImages()
    
    print("🤖 ARABIC SIGN LANGUAGE DATA COLLECTION WITH IMAGES")
    print("="*70)
    print("This system will collect both images and landmark data")
    print("for Arabic sign language recognition training.")
    print("="*70)
    
    # Show word list
    collector.show_word_list()
    
    # Choose collection type
    print("📋 COLLECTION OPTIONS:")
    print("1. Collect data for ALL words (with images)")
    print("2. Collect data for ONE specific word")
    print("3. Show word list again")
    
    choice = input("\nChoose option (1-3): ").strip()
    
    if choice == "1":
        samples = input("Number of samples per word (default 30): ").strip()
        samples = int(samples) if samples.isdigit() else 30
        collector.collect_all_data(samples)
        
    elif choice == "2":
        print("\nAVAILABLE WORDS:")
        for i, (arabic, english, movement) in enumerate(collector.word_pairs, 1):
            print(f"{i}. {english} ({arabic})")
        
        word_idx = input("Choose word number: ").strip()
        if word_idx.isdigit() and 1 <= int(word_idx) <= len(collector.word_pairs):
            word_idx = int(word_idx) - 1
            arabic, english, movement = collector.word_pairs[word_idx]
            samples = input("Number of samples (default 30): ").strip()
            samples = int(samples) if samples.isdigit() else 30
            
            word_data = collector.collect_word_data(arabic, english, movement, samples)
            filename = f"{english.replace(' ', '_').replace('-', '_')}_complete_data.json"
            collector.save_combined_data(word_data, filename)
        else:
            print("❌ Invalid word number")
            
    elif choice == "3":
        collector.show_word_list()
    
    print("\n👋 Thank you for using the data collection system!")
    print("💡 Your images and landmark data are ready for analysis!")
