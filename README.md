# 🤖 نظام التعرف على لغة الإشارة العربية
## Arabic Sign Language Recognition System

نظام ذكي متكامل للتعرف على لغة الإشارة العربية باستخدام الذكاء الاصطناعي وتقنيات الرؤية الحاسوبية المتقدمة.

## 🎯 الهدف

تطوير نظام يتعرف على الكلمات التالية بلغة الإشارة العربية:

### الكلمات المدعومة:
1. **السلام عليكم**
2. **وعليكم السلام ورحمة الله وبركاته**
3. **لو** - **العالم** - **كله** - **سكت**
4. **هتعمل** - **ايه** - **هتكلم** - **بايدي**
5. **وهخلي** - **العالم** - **يسمعني**

### الجملة المستهدفة:
> "لو العالم كله سكت، هتعمل ايه؟ هتكلم بايدي وهخلي العالم يسمعني"

## 🛠️ التقنيات المستخدمة

- **Python 3.8+**
- **OpenCV** - معالجة الصور والفيديو
- **MediaPipe** - اكتشاف وتتبع اليدين
- **TensorFlow** - التعلم العميق
- **Flask** - واجهة الويب
- **Bootstrap** - تصميم الواجهة

## 📁 هيكل المشروع

```
doic/
├── data_collection.py      # جمع بيانات الإشارات
├── model.py               # نموذج التعرف على الإشارات
├── train_model.py         # تدريب النموذج
├── app.py                 # تطبيق الويب
├── requirements.txt       # المكتبات المطلوبة
├── templates/
│   └── index.html        # واجهة المستخدم
├── sign_data/            # بيانات الإشارات (يتم إنشاؤها)
├── models/               # النماذج المدربة (يتم إنشاؤها)
└── plots/                # الرسوم البيانية (يتم إنشاؤها)
```

## 🚀 التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. جمع البيانات

```bash
python data_collection.py
```

**تعليمات جمع البيانات:**
- اختر "1" لجمع البيانات لجميع الكلمات
- تأكد من وجود إضاءة جيدة
- ضع يديك بوضوح أمام الكاميرا
- اضغط SPACE لتسجيل كل إشارة
- اجمع 30-50 عينة لكل كلمة للحصول على أفضل النتائج

### 3. تدريب النموذج

```bash
python train_model.py
```

سيقوم البرنامج بـ:
- فحص جودة البيانات
- تدريب النموذج
- حفظ النموذج المدرب
- إنشاء رسوم بيانية للتدريب

### 4. تشغيل التطبيق

```bash
python app.py
```

ثم افتح المتصفح على: `http://localhost:5000`

## 🎮 كيفية الاستخدام

### واجهة الويب:
1. **البث المباشر**: عرض الكاميرا مع اكتشاف اليدين
2. **التنبؤ الحالي**: عرض الكلمة المتوقعة مع مستوى الثقة
3. **الجملة المكونة**: تجميع الكلمات لتكوين جملة كاملة
4. **أزرار التحكم**: مسح الجملة وإضافة كلمات يدوياً

### نصائح للاستخدام:
- تأكد من وجود إضاءة جيدة
- ضع يديك بوضوح أمام الكاميرا
- حافظ على ثبات الإشارة لثوانٍ قليلة
- تجنب الحركات السريعة

## 📊 مميزات النظام

### 🔍 دقة عالية
- استخدام MediaPipe لاكتشاف دقيق لليدين
- شبكة عصبية عميقة للتصنيف
- معالجة متقدمة للبيانات

### 🎨 واجهة جميلة
- تصميم عصري ومتجاوب
- عرض مباشر للنتائج
- مؤشرات بصرية للثقة والحالة

### ⚡ أداء سريع
- معالجة في الوقت الفعلي
- استجابة فورية
- تحديث مستمر للنتائج

### 🔧 سهولة الاستخدام
- واجهة بديهية
- تعليمات واضحة
- إمكانية إضافة كلمات يدوياً

## 🎥 إعداد للتصوير

النظام مصمم خصيصاً لإنتاج فيديوهات احترافية:

### إعدادات الكاميرا:
- دقة عالية للصورة
- إضاءة متوازنة
- خلفية نظيفة

### عناصر بصرية:
- مؤشرات ملونة للحالة
- رسوم متحركة للتفاعل
- عرض واضح للنتائج

### تسجيل الفيديو:
- استخدم برامج تسجيل الشاشة
- اضبط جودة التسجيل على أعلى مستوى
- تأكد من وضوح الصوت إذا كان مطلوباً

## 🔧 التخصيص

### إضافة كلمات جديدة:
1. أضف الكلمة الجديدة في قائمة `words` في `data_collection.py`
2. اجمع البيانات للكلمة الجديدة
3. أعد تدريب النموذج
4. أضف زر الكلمة في `templates/index.html`

### تحسين الدقة:
- زيادة عدد العينات لكل كلمة
- تحسين جودة البيانات
- ضبط معاملات النموذج
- زيادة عدد طبقات الشبكة العصبية

## 🐛 حل المشاكل الشائعة

### النموذج لا يتعرف على الإشارات:
- تأكد من تدريب النموذج أولاً
- تحقق من وجود ملفات النموذج في مجلد `models/`
- تأكد من جودة البيانات المجمعة

### الكاميرا لا تعمل:
- تحقق من اتصال الكاميرا
- تأكد من عدم استخدام الكاميرا من برنامج آخر
- جرب تغيير رقم الكاميرا في الكود

### دقة منخفضة:
- اجمع المزيد من العينات
- تحسن الإضاءة
- تأكد من وضوح الإشارات

## 📈 تطوير مستقبلي

- إضافة المزيد من الكلمات
- تحسين دقة النموذج
- إضافة ميزة حفظ الجمل
- دعم لغات إشارة أخرى
- تطبيق موبايل

## 👨‍💻 المطور

تم تطوير هذا النظام بواسطة الذكاء الاصطناعي لمساعدة المجتمع في التواصل بلغة الإشارة.

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتطويري.

---

**ملاحظة**: تأكد من وجود كاميرا ويب متصلة بالجهاز قبل تشغيل النظام.
