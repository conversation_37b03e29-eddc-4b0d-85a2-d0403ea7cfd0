#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف التشغيل السريع لنظام التعرف على لغة الإشارة
Quick Start for Sign Language Recognition System
"""

import os
import sys

def print_welcome():
    """طباعة رسالة الترحيب"""
    print("=" * 70)
    print("🤖 مرحباً بك في نظام التعرف على لغة الإشارة")
    print("   Welcome to Sign Language Recognition System")
    print("=" * 70)
    print()

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print("   Error: Python 3.8+ required")
        return False
    print(f"✅ Python {sys.version.split()[0]} - متوافق")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المكتبات المطلوبة...")
    print("   Installing required packages...")
    
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت المكتبات بنجاح")
            print("   Packages installed successfully")
            return True
        else:
            print("❌ فشل في تثبيت المكتبات")
            print("   Failed to install packages")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False

def check_camera():
    """التحقق من الكاميرا"""
    print("\n📷 التحقق من الكاميرا...")
    print("   Checking camera...")
    
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            if ret:
                print("✅ الكاميرا تعمل بشكل صحيح")
                print("   Camera is working properly")
                return True
            else:
                print("⚠️ الكاميرا متصلة لكن لا تعطي صورة")
                print("   Camera connected but no image")
                return False
        else:
            print("❌ لا يمكن الوصول للكاميرا")
            print("   Cannot access camera")
            return False
    except ImportError:
        print("⚠️ OpenCV غير مثبت - سيتم تثبيته")
        print("   OpenCV not installed - will be installed")
        return True
    except Exception as e:
        print(f"❌ خطأ في الكاميرا: {e}")
        return False

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n" + "="*50)
    print("📋 اختر ما تريد فعله:")
    print("   Choose what you want to do:")
    print("="*50)
    print("1. 📊 جمع البيانات (Data Collection)")
    print("2. 🚀 تدريب النموذج (Train Model)")
    print("3. 🌐 تشغيل التطبيق (Run Application)")
    print("4. 🔄 تشغيل كامل (Full Setup)")
    print("5. ❌ خروج (Exit)")
    print("="*50)

def run_data_collection():
    """تشغيل جمع البيانات"""
    print("\n📊 تشغيل جمع البيانات...")
    try:
        os.system("python data_collection.py")
    except Exception as e:
        print(f"❌ خطأ: {e}")

def run_training():
    """تشغيل التدريب"""
    print("\n🚀 تشغيل تدريب النموذج...")
    try:
        os.system("python train_model.py")
    except Exception as e:
        print(f"❌ خطأ: {e}")

def run_app():
    """تشغيل التطبيق"""
    print("\n🌐 تشغيل التطبيق...")
    print("سيتم فتح التطبيق على: http://localhost:5000")
    print("Application will open at: http://localhost:5000")
    try:
        os.system("python app.py")
    except Exception as e:
        print(f"❌ خطأ: {e}")

def full_setup():
    """الإعداد الكامل"""
    print("\n🔄 بدء الإعداد الكامل...")
    print("   Starting full setup...")
    
    # التحقق من البيانات
    if not os.path.exists("sign_data/all_signs_data.json"):
        print("\n📊 لا توجد بيانات، سيتم جمع البيانات أولاً...")
        print("   No data found, collecting data first...")
        run_data_collection()
    
    # التحقق من النموذج
    if not (os.path.exists("models/sign_language_model.h5") and 
            os.path.exists("models/label_encoder.pkl")):
        print("\n🚀 النموذج غير مدرب، سيتم التدريب أولاً...")
        print("   Model not trained, training first...")
        run_training()
    
    # تشغيل التطبيق
    print("\n🌐 تشغيل التطبيق...")
    print("   Running application...")
    run_app()

def main():
    """الدالة الرئيسية"""
    print_welcome()
    
    # التحقق من Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من الكاميرا
    camera_ok = check_camera()
    if not camera_ok:
        print("⚠️ تحذير: قد تواجه مشاكل مع الكاميرا")
        print("   Warning: You may face camera issues")
    
    # تثبيت المتطلبات إذا لزم الأمر
    try:
        import cv2, mediapipe, tensorflow, flask
        print("✅ جميع المكتبات متوفرة")
        print("   All packages available")
    except ImportError:
        print("📦 بعض المكتبات غير متوفرة، سيتم تثبيتها...")
        print("   Some packages missing, installing...")
        if not install_requirements():
            print("❌ فشل في تثبيت المكتبات")
            input("اضغط Enter للخروج...")
            return
    
    # القائمة الرئيسية
    while True:
        show_menu()
        choice = input("\nاختر رقماً (1-5): ").strip()
        
        if choice == "1":
            run_data_collection()
        elif choice == "2":
            run_training()
        elif choice == "3":
            run_app()
        elif choice == "4":
            full_setup()
            break
        elif choice == "5":
            print("\n👋 شكراً لاستخدام النظام!")
            print("   Thanks for using the system!")
            break
        else:
            print("❌ اختيار غير صحيح")
            print("   Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بواسطة المستخدم")
        print("   System stopped by user")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        print("   Unexpected error occurred")
        input("اضغط Enter للخروج...")
