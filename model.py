import cv2
import mediapipe as mp
import numpy as np
import tensorflow as tf
from tensorflow import keras
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import json
import os
import joblib

class SignLanguageModel:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        self.model = None
        self.label_encoder = None
        self.words = [
            "السلام عليكم",
            "وعليكم السلام ورحمة الله وبركاته", 
            "لو",
            "العالم",
            "كله",
            "سكت",
            "هتعمل",
            "ايه",
            "هتكلم",
            "بايدي",
            "وهخلي",
            "العالم",
            "يسمعني"
        ]
    
    def extract_landmarks(self, results):
        """استخراج النقاط المميزة من اليدين"""
        landmarks = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                hand_points = []
                for landmark in hand_landmarks.landmark:
                    hand_points.extend([landmark.x, landmark.y, landmark.z])
                landmarks.extend(hand_points)
        
        # إذا لم يتم اكتشاف يدين، املأ بالأصفار
        while len(landmarks) < 126:  # 21 نقطة × 3 إحداثيات × 2 يد
            landmarks.append(0.0)
            
        return landmarks[:126]
    
    def load_data(self, data_file="sign_data/all_signs_data.json"):
        """تحميل البيانات من ملف JSON"""
        if not os.path.exists(data_file):
            raise FileNotFoundError(f"ملف البيانات غير موجود: {data_file}")
        
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        X = []
        y = []
        
        for sample in data:
            X.append(sample['landmarks'])
            y.append(sample['word'])
        
        return np.array(X), np.array(y)
    
    def create_model(self, input_shape, num_classes):
        """إنشاء نموذج الشبكة العصبية"""
        model = keras.Sequential([
            keras.layers.Dense(512, activation='relu', input_shape=(input_shape,)),
            keras.layers.Dropout(0.3),
            keras.layers.Dense(256, activation='relu'),
            keras.layers.Dropout(0.3),
            keras.layers.Dense(128, activation='relu'),
            keras.layers.Dropout(0.2),
            keras.layers.Dense(64, activation='relu'),
            keras.layers.Dense(num_classes, activation='softmax')
        ])
        
        model.compile(
            optimizer='adam',
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def train_model(self, data_file="sign_data/all_signs_data.json"):
        """تدريب النموذج"""
        print("تحميل البيانات...")
        X, y = self.load_data(data_file)
        
        print(f"تم تحميل {len(X)} عينة")
        
        # تحويل التسميات إلى أرقام
        self.label_encoder = LabelEncoder()
        y_encoded = self.label_encoder.fit_transform(y)
        
        # تقسيم البيانات
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
        )
        
        print(f"بيانات التدريب: {len(X_train)}")
        print(f"بيانات الاختبار: {len(X_test)}")
        
        # إنشاء النموذج
        self.model = self.create_model(X.shape[1], len(self.label_encoder.classes_))
        
        print("بدء التدريب...")
        
        # تدريب النموذج
        history = self.model.fit(
            X_train, y_train,
            epochs=100,
            batch_size=32,
            validation_data=(X_test, y_test),
            verbose=1
        )
        
        # تقييم النموذج
        test_loss, test_accuracy = self.model.evaluate(X_test, y_test, verbose=0)
        print(f"\nدقة النموذج على بيانات الاختبار: {test_accuracy:.4f}")
        
        return history
    
    def save_model(self, model_path="models"):
        """حفظ النموذج والمشفر"""
        if not os.path.exists(model_path):
            os.makedirs(model_path)
        
        # حفظ النموذج
        self.model.save(os.path.join(model_path, "sign_language_model.h5"))
        
        # حفظ المشفر
        joblib.dump(self.label_encoder, os.path.join(model_path, "label_encoder.pkl"))
        
        print(f"تم حفظ النموذج في: {model_path}")
    
    def load_model(self, model_path="models"):
        """تحميل النموذج والمشفر"""
        model_file = os.path.join(model_path, "sign_language_model.h5")
        encoder_file = os.path.join(model_path, "label_encoder.pkl")
        
        if os.path.exists(model_file) and os.path.exists(encoder_file):
            self.model = keras.models.load_model(model_file)
            self.label_encoder = joblib.load(encoder_file)
            print("تم تحميل النموذج بنجاح")
            return True
        else:
            print("ملفات النموذج غير موجودة")
            return False
    
    def predict_sign(self, landmarks):
        """التنبؤ بالإشارة من النقاط المميزة"""
        if self.model is None or self.label_encoder is None:
            return None, 0.0
        
        landmarks = np.array(landmarks).reshape(1, -1)
        predictions = self.model.predict(landmarks, verbose=0)
        
        predicted_class = np.argmax(predictions[0])
        confidence = predictions[0][predicted_class]
        
        predicted_word = self.label_encoder.inverse_transform([predicted_class])[0]
        
        return predicted_word, confidence
    
    def real_time_prediction(self):
        """التنبؤ في الوقت الفعلي"""
        if not self.load_model():
            print("يجب تدريب النموذج أولاً!")
            return
        
        cap = cv2.VideoCapture(0)
        
        print("اضغط ESC للخروج")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            # رسم النقاط المميزة
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS
                    )
                
                # التنبؤ
                landmarks = self.extract_landmarks(results)
                predicted_word, confidence = self.predict_sign(landmarks)
                
                if confidence > 0.7:  # عتبة الثقة
                    cv2.putText(frame, f"Word: {predicted_word}", (10, 30),
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                    cv2.putText(frame, f"Confidence: {confidence:.2f}", (10, 70),
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                else:
                    cv2.putText(frame, "غير واضح", (10, 30),
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            else:
                cv2.putText(frame, "لا توجد يدين", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            cv2.imshow('Sign Language Recognition', frame)
            
            if cv2.waitKey(1) & 0xFF == 27:  # ESC
                break
        
        cap.release()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    model = SignLanguageModel()
    
    choice = input("1. تدريب النموذج\n2. اختبار النموذج\nاختر (1 أو 2): ")
    
    if choice == "1":
        model.train_model()
        model.save_model()
    elif choice == "2":
        model.real_time_prediction()
