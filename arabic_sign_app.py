#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Beautiful Arabic Sign Language Recognition Web App
تطبيق ويب جميل للتعرف على لغة الإشارة العربية
"""

from flask import Flask, render_template, Response, jsonify
import cv2
import mediapipe as mp
import numpy as np
import json
import os
import joblib
from datetime import datetime

app = Flask(__name__)

class ArabicSignLanguageApp:
    def __init__(self):
        # Initialize MediaPipe
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.8,
            min_tracking_confidence=0.7
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Load model
        self.model = None
        self.label_encoder = None
        self.load_model()
        
        # App state
        self.current_prediction = ""
        self.current_confidence = 0.0
        self.is_hands_detected = False
        self.sentence = []
        self.last_prediction = ""
        self.prediction_count = 0
        self.required_count = 10  # Required consecutive predictions
        
        # Target sentence and words
        self.target_sentence_arabic = "لو العالم كله سكت، هتعمل ايه؟ هتكلم بايدي وهخلي العالم يسمعني"
        self.target_words = [
            "Law", "Al-alam", "Kullu", "Sakat", 
            "Hat'amal", "Eh", "Hatkallam", "Bi-yadi", 
            "Wa-hakhalli", "Al-alam", "Yasma'ni"
        ]
        
        # Arabic to English mapping
        self.arabic_to_english = {
            "السلام عليكم": "As-salamu alaykum",
            "وعليكم السلام ورحمة الله وبركاته": "Wa alaykumu s-salamu",
            "لو": "Law",
            "العالم": "Al-alam",
            "كله": "Kullu",
            "سكت": "Sakat",
            "هتعمل": "Hat'amal",
            "ايه": "Eh",
            "هتكلم": "Hatkallam",
            "بايدي": "Bi-yadi",
            "وهخلي": "Wa-hakhalli",
            "يسمعني": "Yasma'ni"
        }
        
        # English to Arabic mapping
        self.english_to_arabic = {v: k for k, v in self.arabic_to_english.items()}
    
    def load_model(self):
        """Load the trained model"""
        model_path = "arabic_models/arabic_sign_model.pkl"
        encoder_path = "arabic_models/arabic_label_encoder.pkl"
        
        if os.path.exists(model_path) and os.path.exists(encoder_path):
            try:
                self.model = joblib.load(model_path)
                self.label_encoder = joblib.load(encoder_path)
                print(f"✅ تم تحميل النموذج: {len(self.label_encoder.classes_)} كلمة")
                return True
            except Exception as e:
                print(f"❌ خطأ في تحميل النموذج: {e}")
                return False
        else:
            print("❌ ملفات النموذج غير موجودة")
            return False
    
    def extract_landmarks(self, results):
        """Extract landmarks from hands"""
        landmarks = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                hand_points = []
                for landmark in hand_landmarks.landmark:
                    hand_points.extend([landmark.x, landmark.y, landmark.z])
                landmarks.extend(hand_points)
        
        # Fill with zeros if less than 2 hands detected
        while len(landmarks) < 126:  # 21 points × 3 coordinates × 2 hands
            landmarks.append(0.0)
            
        return landmarks[:126]
    
    def predict_sign(self, landmarks):
        """Predict word from landmarks"""
        if self.model is None or self.label_encoder is None:
            return "Unknown", 0.0
        
        try:
            landmarks = np.array(landmarks).reshape(1, -1)
            
            # Get prediction and probability
            prediction = self.model.predict(landmarks)[0]
            probabilities = self.model.predict_proba(landmarks)[0]
            confidence = probabilities[prediction]
            
            # Convert back to word
            predicted_word = self.label_encoder.inverse_transform([prediction])[0]
            
            return predicted_word, confidence
        except Exception as e:
            print(f"خطأ في التنبؤ: {e}")
            return "Unknown", 0.0
    
    def generate_frames(self):
        """Generate frames for video streaming"""
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ Cannot access camera")
            return
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            # Draw hand landmarks
            self.is_hands_detected = False
            if results.multi_hand_landmarks:
                self.is_hands_detected = True
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS,
                        self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=3),
                        self.mp_drawing.DrawingSpec(color=(255, 0, 0), thickness=3)
                    )
                
                # Make prediction
                landmarks = self.extract_landmarks(results)
                predicted_word, confidence = self.predict_sign(landmarks)
                
                if confidence > 0.7:  # Confidence threshold
                    self.current_prediction = predicted_word
                    self.current_confidence = confidence
                    
                    # Add to sentence if prediction is stable
                    if predicted_word == self.last_prediction:
                        self.prediction_count += 1
                    else:
                        self.prediction_count = 1
                        self.last_prediction = predicted_word
                    
                    if self.prediction_count >= self.required_count:
                        if predicted_word not in self.sentence:
                            self.sentence.append(predicted_word)
                            print(f"✅ تمت إضافة للجملة: {predicted_word}")
                        self.prediction_count = 0
                else:
                    self.current_prediction = ""
                    self.current_confidence = 0.0
            else:
                self.current_prediction = ""
                self.current_confidence = 0.0
            
            # Add info to frame
            self.add_info_to_frame(frame)
            
            # Convert frame to JPEG
            ret, buffer = cv2.imencode('.jpg', frame)
            frame = buffer.tobytes()
            
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
    
    def add_info_to_frame(self, frame):
        """Add information overlay to frame"""
        height, width = frame.shape[:2]
        
        # Semi-transparent background
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (width-10, 200), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Hand detection status
        if self.is_hands_detected:
            status_text = "Hands Detected"
            status_color = (0, 255, 0)
        else:
            status_text = "Place hands in camera"
            status_color = (0, 0, 255)
        
        cv2.putText(frame, status_text, (20, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, status_color, 2)
        
        # Current prediction
        if self.current_prediction and self.current_confidence > 0.7:
            # English word
            cv2.putText(frame, f"Word: {self.current_prediction}", (20, 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
            
            # Arabic word
            arabic_word = self.english_to_arabic.get(self.current_prediction, "")
            if arabic_word:
                cv2.putText(frame, arabic_word, (20, 110),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
            
            cv2.putText(frame, f"Confidence: {self.current_confidence:.2f}", (20, 140),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        else:
            cv2.putText(frame, "Waiting for sign...", (20, 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
        
        # Sentence progress
        progress = len(self.sentence)
        total = len(self.target_words)
        cv2.putText(frame, f"Progress: {progress}/{total} words", (20, 170),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

# Create app instance
sign_app = ArabicSignLanguageApp()

@app.route('/')
def index():
    """Main page"""
    return render_template('arabic_sign_index.html')

@app.route('/video_feed')
def video_feed():
    """Video streaming route"""
    return Response(sign_app.generate_frames(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/get_prediction')
def get_prediction():
    """Get current prediction"""
    arabic_word = ""
    if sign_app.current_prediction:
        arabic_word = sign_app.english_to_arabic.get(sign_app.current_prediction, "")
    
    return jsonify({
        'prediction_english': sign_app.current_prediction,
        'prediction_arabic': arabic_word,
        'confidence': sign_app.current_confidence,
        'hands_detected': sign_app.is_hands_detected,
        'sentence': sign_app.sentence,
        'sentence_arabic': [sign_app.english_to_arabic.get(word, word) for word in sign_app.sentence],
        'target_sentence': sign_app.target_sentence_arabic,
        'target_words': sign_app.target_words,
        'progress': len(sign_app.sentence),
        'total_words': len(sign_app.target_words)
    })

@app.route('/clear_sentence')
def clear_sentence():
    """Clear current sentence"""
    sign_app.sentence = []
    return jsonify({'status': 'success'})

@app.route('/add_word/<word>')
def add_word(word):
    """Add word to sentence manually"""
    if word not in sign_app.sentence:
        sign_app.sentence.append(word)
    return jsonify({'status': 'success', 'sentence': sign_app.sentence})

@app.route('/get_words')
def get_words():
    """Get available words"""
    if sign_app.label_encoder:
        words = sign_app.label_encoder.classes_.tolist()
    else:
        words = []
    
    return jsonify({
        'words': words,
        'arabic_words': [sign_app.english_to_arabic.get(word, word) for word in words],
        'target_sentence': sign_app.target_sentence_arabic,
        'target_words': sign_app.target_words
    })

if __name__ == '__main__':
    print("🚀 تطبيق التعرف على لغة الإشارة العربية")
    print("="*50)
    
    if sign_app.model is not None:
        print("🤖 النموذج محمل وجاهز")
        print(f"📋 الكلمات المدعومة: {len(sign_app.label_encoder.classes_)}")
        print("🎯 الجملة المستهدفة:")
        print(f"   {sign_app.target_sentence_arabic}")
    else:
        print("❌ النموذج غير محمل - يرجى التدريب أولاً")
        print("   python train_arabic_model.py")
    
    print("="*50)
    print("🌐 بدء خادم الويب...")
    print("📱 افتح المتصفح على: http://localhost:5000")
    print("⏹️ اضغط Ctrl+C للإيقاف")
    print("="*50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
