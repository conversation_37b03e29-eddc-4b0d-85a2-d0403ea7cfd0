import cv2
import mediapipe as mp
import numpy as np
import os
import json
import time
from datetime import datetime

class SignLanguageDataCollector:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # الكلمات المطلوب جمع بياناتها
        self.words = [
            "السلام عليكم",
            "وعليكم السلام ورحمة الله وبركاته", 
            "لو",
            "العالم",
            "كله",
            "سكت",
            "هتعمل",
            "ايه",
            "هتكلم",
            "بايدي",
            "وهخلي",
            "العالم",
            "يسمعني"
        ]
        
        self.data_dir = "sign_data"
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def extract_landmarks(self, results):
        """استخراج النقاط المميزة من اليدين"""
        landmarks = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                hand_points = []
                for landmark in hand_landmarks.landmark:
                    hand_points.extend([landmark.x, landmark.y, landmark.z])
                landmarks.extend(hand_points)
        
        # إذا لم يتم اكتشاف يدين، املأ بالأصفار
        while len(landmarks) < 126:  # 21 نقطة × 3 إحداثيات × 2 يد
            landmarks.append(0.0)
            
        return landmarks[:126]  # تأكد من الحجم الثابت
    
    def collect_word_data(self, word, samples_per_word=50):
        """جمع البيانات لكلمة معينة"""
        cap = cv2.VideoCapture(0)
        word_data = []
        sample_count = 0
        
        print(f"\nجمع البيانات للكلمة: {word}")
        print(f"اضغط SPACE لتسجيل عينة، ESC للانتهاء")
        print(f"المطلوب: {samples_per_word} عينة")
        
        while sample_count < samples_per_word:
            ret, frame = cap.read()
            if not ret:
                break
                
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            # رسم النقاط المميزة
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS
                    )
            
            # عرض المعلومات
            cv2.putText(frame, f"Word: {word}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Samples: {sample_count}/{samples_per_word}", 
                       (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, "Press SPACE to record, ESC to exit", 
                       (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.imshow('Sign Language Data Collection', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord(' '):  # مسطرة المسافة لتسجيل عينة
                if results.multi_hand_landmarks:
                    landmarks = self.extract_landmarks(results)
                    word_data.append({
                        'word': word,
                        'landmarks': landmarks,
                        'timestamp': datetime.now().isoformat()
                    })
                    sample_count += 1
                    print(f"تم تسجيل العينة {sample_count}")
                else:
                    print("لم يتم اكتشاف يدين! حاول مرة أخرى")
                    
            elif key == 27:  # ESC للخروج
                break
        
        cap.release()
        cv2.destroyAllWindows()
        
        return word_data
    
    def save_data(self, data, filename):
        """حفظ البيانات في ملف JSON"""
        filepath = os.path.join(self.data_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"تم حفظ البيانات في: {filepath}")
    
    def collect_all_data(self, samples_per_word=30):
        """جمع البيانات لجميع الكلمات"""
        all_data = []
        
        for word in self.words:
            print(f"\n{'='*50}")
            print(f"استعد لتسجيل كلمة: {word}")
            input("اضغط Enter للمتابعة...")
            
            word_data = self.collect_word_data(word, samples_per_word)
            all_data.extend(word_data)
            
            # حفظ البيانات لكل كلمة منفصلة
            word_filename = f"{word.replace(' ', '_')}_data.json"
            self.save_data(word_data, word_filename)
        
        # حفظ جميع البيانات في ملف واحد
        self.save_data(all_data, "all_signs_data.json")
        print(f"\nتم جمع {len(all_data)} عينة إجمالية")

if __name__ == "__main__":
    collector = SignLanguageDataCollector()
    
    print("مرحباً بك في برنامج جمع بيانات لغة الإشارة")
    print("تأكد من وجود إضاءة جيدة ووضوح اليدين أمام الكاميرا")
    
    # اختيار نوع الجمع
    choice = input("\n1. جمع البيانات لجميع الكلمات\n2. جمع البيانات لكلمة واحدة\nاختر (1 أو 2): ")
    
    if choice == "1":
        samples = int(input("عدد العينات لكل كلمة (افتراضي 30): ") or "30")
        collector.collect_all_data(samples)
    elif choice == "2":
        print("\nالكلمات المتاحة:")
        for i, word in enumerate(collector.words, 1):
            print(f"{i}. {word}")
        
        word_idx = int(input("اختر رقم الكلمة: ")) - 1
        if 0 <= word_idx < len(collector.words):
            word = collector.words[word_idx]
            samples = int(input("عدد العينات (افتراضي 30): ") or "30")
            word_data = collector.collect_word_data(word, samples)
            filename = f"{word.replace(' ', '_')}_data.json"
            collector.save_data(word_data, filename)
    
    print("\nتم الانتهاء من جمع البيانات!")
