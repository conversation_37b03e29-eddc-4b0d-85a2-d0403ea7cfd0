#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Data Analysis Tool for Collected Sign Language Data
أداة تحليل البيانات المجمعة للغة الإشارة
"""

import os
import json
import cv2
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import pandas as pd

class SignDataAnalyzer:
    def __init__(self):
        self.data_dir = "sign_data"
        self.images_dir = "sign_images"
        self.landmarks_dir = "landmarks_data"
        self.analysis_dir = "data_analysis"
        
        # Create analysis directory
        if not os.path.exists(self.analysis_dir):
            os.makedirs(self.analysis_dir)
    
    def load_all_data(self):
        """Load all collected data"""
        print("📊 Loading collected data...")
        
        # Load combined data if exists
        combined_file = os.path.join(self.data_dir, "all_signs_complete_data.json")
        if os.path.exists(combined_file):
            with open(combined_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✅ Loaded {len(data)} samples from combined file")
            return data
        
        # Otherwise load individual files
        all_data = []
        if os.path.exists(self.landmarks_dir):
            for filename in os.listdir(self.landmarks_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(self.landmarks_dir, filename)
                    with open(filepath, 'r', encoding='utf-8') as f:
                        sample_data = json.load(f)
                        all_data.append(sample_data)
        
        print(f"✅ Loaded {len(all_data)} samples from individual files")
        return all_data
    
    def analyze_data_distribution(self, data):
        """Analyze data distribution"""
        print("\n📈 Analyzing data distribution...")
        
        # Count samples per word
        word_counts = {}
        hand_detection_counts = {}
        
        for sample in data:
            word = sample.get('word_english', 'Unknown')
            word_counts[word] = word_counts.get(word, 0) + 1
            
            has_hands = sample.get('hand_detected', False)
            hand_detection_counts[word] = hand_detection_counts.get(word, {'detected': 0, 'not_detected': 0})
            if has_hands:
                hand_detection_counts[word]['detected'] += 1
            else:
                hand_detection_counts[word]['not_detected'] += 1
        
        # Create distribution plot
        plt.figure(figsize=(15, 8))
        
        # Samples per word
        plt.subplot(2, 2, 1)
        words = list(word_counts.keys())
        counts = list(word_counts.values())
        plt.bar(words, counts, color='skyblue')
        plt.title('Samples per Word')
        plt.xlabel('Words')
        plt.ylabel('Number of Samples')
        plt.xticks(rotation=45, ha='right')
        
        # Hand detection rate
        plt.subplot(2, 2, 2)
        detection_rates = []
        for word in words:
            total = hand_detection_counts[word]['detected'] + hand_detection_counts[word]['not_detected']
            rate = hand_detection_counts[word]['detected'] / total * 100 if total > 0 else 0
            detection_rates.append(rate)
        
        plt.bar(words, detection_rates, color='lightgreen')
        plt.title('Hand Detection Rate (%)')
        plt.xlabel('Words')
        plt.ylabel('Detection Rate (%)')
        plt.xticks(rotation=45, ha='right')
        plt.ylim(0, 100)
        
        # Overall statistics
        plt.subplot(2, 2, 3)
        total_samples = len(data)
        total_with_hands = sum(1 for sample in data if sample.get('hand_detected', False))
        detection_rate = total_with_hands / total_samples * 100 if total_samples > 0 else 0
        
        stats = ['Total Samples', 'With Hands', 'Detection Rate (%)']
        values = [total_samples, total_with_hands, detection_rate]
        colors = ['lightblue', 'lightgreen', 'orange']
        
        plt.bar(stats, values, color=colors)
        plt.title('Overall Statistics')
        plt.ylabel('Count / Percentage')
        
        # Data quality pie chart
        plt.subplot(2, 2, 4)
        labels = ['With Hands', 'Without Hands']
        sizes = [total_with_hands, total_samples - total_with_hands]
        colors = ['lightgreen', 'lightcoral']
        
        plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        plt.title('Data Quality Distribution')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.analysis_dir, 'data_distribution.png'), dpi=300, bbox_inches='tight')
        plt.show()
        
        return word_counts, hand_detection_counts
    
    def analyze_landmarks(self, data):
        """Analyze landmark patterns"""
        print("\n🤲 Analyzing landmark patterns...")
        
        # Extract landmarks for samples with hands
        landmarks_by_word = {}
        
        for sample in data:
            if sample.get('hand_detected', False):
                word = sample.get('word_english', 'Unknown')
                landmarks = sample.get('landmarks', [])
                
                if word not in landmarks_by_word:
                    landmarks_by_word[word] = []
                landmarks_by_word[word].append(landmarks)
        
        # Calculate average landmarks per word
        avg_landmarks = {}
        for word, landmark_list in landmarks_by_word.items():
            if landmark_list:
                avg_landmarks[word] = np.mean(landmark_list, axis=0)
        
        # Visualize landmark patterns
        if avg_landmarks:
            plt.figure(figsize=(20, 12))
            
            # Plot first few words as examples
            words_to_plot = list(avg_landmarks.keys())[:6]
            
            for i, word in enumerate(words_to_plot):
                plt.subplot(2, 3, i+1)
                landmarks = avg_landmarks[word]
                
                # Extract x, y coordinates for visualization
                x_coords = landmarks[0::3]  # Every 3rd element starting from 0
                y_coords = landmarks[1::3]  # Every 3rd element starting from 1
                
                plt.scatter(x_coords, y_coords, alpha=0.7)
                plt.title(f'{word}\n({len(landmarks_by_word[word])} samples)')
                plt.xlabel('X Coordinate')
                plt.ylabel('Y Coordinate')
                plt.gca().invert_yaxis()  # Invert Y axis to match image coordinates
            
            plt.tight_layout()
            plt.savefig(os.path.join(self.analysis_dir, 'landmark_patterns.png'), dpi=300, bbox_inches='tight')
            plt.show()
        
        return landmarks_by_word
    
    def generate_report(self, data, word_counts, hand_detection_counts):
        """Generate detailed analysis report"""
        print("\n📋 Generating analysis report...")
        
        report = []
        report.append("# Sign Language Data Analysis Report")
        report.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Overall statistics
        total_samples = len(data)
        total_with_hands = sum(1 for sample in data if sample.get('hand_detected', False))
        overall_detection_rate = total_with_hands / total_samples * 100 if total_samples > 0 else 0
        
        report.append("## Overall Statistics")
        report.append(f"- Total samples collected: {total_samples}")
        report.append(f"- Samples with hands detected: {total_with_hands}")
        report.append(f"- Overall hand detection rate: {overall_detection_rate:.1f}%")
        report.append(f"- Number of unique words: {len(word_counts)}")
        report.append("")
        
        # Per-word statistics
        report.append("## Per-Word Statistics")
        report.append("| Word | Samples | Hands Detected | Detection Rate |")
        report.append("|------|---------|----------------|----------------|")
        
        for word in sorted(word_counts.keys()):
            count = word_counts[word]
            detected = hand_detection_counts[word]['detected']
            rate = detected / count * 100 if count > 0 else 0
            report.append(f"| {word} | {count} | {detected} | {rate:.1f}% |")
        
        report.append("")
        
        # Data quality assessment
        report.append("## Data Quality Assessment")
        
        # Find words with low detection rates
        low_detection_words = []
        for word, counts in hand_detection_counts.items():
            total = counts['detected'] + counts['not_detected']
            rate = counts['detected'] / total * 100 if total > 0 else 0
            if rate < 80:  # Less than 80% detection rate
                low_detection_words.append((word, rate))
        
        if low_detection_words:
            report.append("### Words with Low Hand Detection Rates (< 80%):")
            for word, rate in low_detection_words:
                report.append(f"- {word}: {rate:.1f}%")
        else:
            report.append("✅ All words have good hand detection rates (≥ 80%)")
        
        report.append("")
        
        # Recommendations
        report.append("## Recommendations")
        
        if overall_detection_rate < 90:
            report.append("- Consider improving lighting conditions")
            report.append("- Ensure hands are clearly visible in camera frame")
            report.append("- Re-collect samples for words with low detection rates")
        
        min_samples = min(word_counts.values()) if word_counts else 0
        if min_samples < 30:
            report.append("- Some words have fewer than 30 samples")
            report.append("- Consider collecting more samples for better model training")
        
        report.append("- Review collected images manually for quality")
        report.append("- Proceed with model training if detection rates are satisfactory")
        
        # Save report
        report_path = os.path.join(self.analysis_dir, 'analysis_report.md')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"📄 Report saved to: {report_path}")
        
        # Print summary to console
        print("\n" + "="*60)
        print("📊 ANALYSIS SUMMARY")
        print("="*60)
        print(f"Total samples: {total_samples}")
        print(f"Hand detection rate: {overall_detection_rate:.1f}%")
        print(f"Words analyzed: {len(word_counts)}")
        
        if low_detection_words:
            print(f"⚠️ Words needing attention: {len(low_detection_words)}")
            for word, rate in low_detection_words[:3]:  # Show first 3
                print(f"   - {word}: {rate:.1f}%")
        else:
            print("✅ All words have good detection rates")
        
        print("="*60)
    
    def run_analysis(self):
        """Run complete analysis"""
        print("🔍 SIGN LANGUAGE DATA ANALYSIS")
        print("="*60)
        
        # Load data
        data = self.load_all_data()
        
        if not data:
            print("❌ No data found to analyze")
            print("   Make sure you have collected data first using data_collection_with_images.py")
            return
        
        # Run analyses
        word_counts, hand_detection_counts = self.analyze_data_distribution(data)
        landmarks_by_word = self.analyze_landmarks(data)
        self.generate_report(data, word_counts, hand_detection_counts)
        
        print(f"\n✅ Analysis complete! Check the '{self.analysis_dir}' folder for:")
        print("   - data_distribution.png")
        print("   - landmark_patterns.png")
        print("   - analysis_report.md")

if __name__ == "__main__":
    analyzer = SignDataAnalyzer()
    analyzer.run_analysis()
