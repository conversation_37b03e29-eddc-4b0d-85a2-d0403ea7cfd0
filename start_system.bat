@echo off
chcp 65001 >nul
title نظام التعرف على لغة الإشارة العربية

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║        🤖 نظام التعرف على لغة الإشارة العربية 🤖            ║
echo ║                                                              ║
echo ║              مرحباً بك في النظام الذكي                      ║
echo ║            للتعرف على لغة الإشارة العربية                   ║
echo ║                                                              ║
echo ║    الكلمات المدعومة: السلام عليكم، لو العالم كله سكت...      ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python 3.8 أو أحدث
    echo    تحميل Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 📋 اختر طريقة التشغيل:
echo 1. 🎮 عرض توضيحي سريع (بدون تدريب)
echo 2. 🚀 النظام الكامل (مع التدريب)
echo 3. 📦 تثبيت المكتبات فقط
echo.

set /p choice="اختر رقماً (1-3): "

if "%choice%"=="1" goto demo
if "%choice%"=="2" goto full
if "%choice%"=="3" goto install
goto invalid

:demo
echo.
echo 🎮 تشغيل العرض التوضيحي...
echo تثبيت المكتبات الأساسية...
python -m pip install opencv-python mediapipe numpy
if errorlevel 1 (
    echo ❌ فشل في تثبيت المكتبات الأساسية
    pause
    exit /b 1
)
echo ✅ تم تثبيت المكتبات الأساسية
echo.
echo 🎥 بدء العرض التوضيحي...
python simple_demo.py
goto end

:full
echo.
echo 🚀 تشغيل النظام الكامل...
echo 📦 تثبيت جميع المكتبات المطلوبة...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ فشل في تثبيت المكتبات
    pause
    exit /b 1
)
echo ✅ تم تثبيت المكتبات بنجاح
echo.
echo 🚀 تشغيل النظام...
python quick_start.py
goto end

:install
echo.
echo 📦 تثبيت المكتبات المطلوبة...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ فشل في تثبيت المكتبات
    pause
    exit /b 1
)
echo ✅ تم تثبيت المكتبات بنجاح
echo.
echo 💡 يمكنك الآن تشغيل النظام باستخدام:
echo    python quick_start.py
goto end

:invalid
echo ❌ اختيار غير صحيح
goto end

:end
echo.
echo 👋 شكراً لاستخدام النظام!
pause
