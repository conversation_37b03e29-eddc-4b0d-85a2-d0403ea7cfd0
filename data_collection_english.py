import cv2
import mediapipe as mp
import numpy as np
import os
import json
import time
from datetime import datetime

class SignLanguageDataCollectorEnglish:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Word pairs: (Arabic, English, Movement Description)
        self.word_pairs = [
            ("السلام عليكم", "As-salamu alaykum", "Right hand open, move left to right + left hand on heart"),
            ("وعليكم السلام ورحمة الله وبركاته", "Wa alaykumu s-salamu...", "Both hands open, circular motion up and out"),
            ("لو", "Law", "Index finger up, move left-right (questioning)"),
            ("العالم", "Al-alam", "Hands in ball shape, circular motion around each other"),
            ("كله", "Kullu", "Open hands wide outward, then bring together"),
            ("سكت", "Sakat", "Index finger on lips (silence gesture)"),
            ("هتعمل", "Hat'amal", "Two fists moving back and forth (working motion)"),
            ("ايه", "Eh", "Raise hands up with open fingers (questioning)"),
            ("هتكلم", "Hatkallam", "Hand in front of mouth, move fingers (talking)"),
            ("بايدي", "Bi-yadi", "Raise both hands with open palms"),
            ("وهخلي", "Wa-hakhalli", "From fist to open hand forward (giving/making)"),
            ("يسمعني", "Yasma'ni", "Hand behind ear then forward (listening)")
        ]
        
        self.data_dir = "sign_data"
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def extract_landmarks(self, results):
        """Extract landmarks from hands"""
        landmarks = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                hand_points = []
                for landmark in hand_landmarks.landmark:
                    hand_points.extend([landmark.x, landmark.y, landmark.z])
                landmarks.extend(hand_points)
        
        # Fill with zeros if less than 2 hands detected
        while len(landmarks) < 126:  # 21 points × 3 coordinates × 2 hands
            landmarks.append(0.0)
            
        return landmarks[:126]  # Ensure fixed size
    
    def collect_word_data(self, arabic_word, english_word, movement_desc, samples_per_word=30):
        """Collect data for a specific word"""
        cap = cv2.VideoCapture(0)
        word_data = []
        sample_count = 0
        
        print(f"\n{'='*60}")
        print(f"📊 COLLECTING DATA FOR WORD #{len(word_data)+1}")
        print(f"{'='*60}")
        print(f"Arabic Word: {arabic_word}")
        print(f"English: {english_word}")
        print(f"Movement: {movement_desc}")
        print(f"Target: {samples_per_word} samples")
        print(f"{'='*60}")
        print("INSTRUCTIONS:")
        print("- Make sure you have good lighting")
        print("- Place hands clearly in front of camera")
        print("- Press SPACE to record each sign")
        print("- Press ESC to skip this word")
        print("- Make the movement clear and distinctive")
        print(f"{'='*60}")
        
        input("Press Enter when ready to start...")
        
        while sample_count < samples_per_word:
            ret, frame = cap.read()
            if not ret:
                break
                
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            # Draw hand landmarks
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS,
                        self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2),
                        self.mp_drawing.DrawingSpec(color=(255, 0, 0), thickness=2)
                    )
            
            # Add info to frame
            cv2.putText(frame, f"Word: {english_word}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Samples: {sample_count}/{samples_per_word}", 
                       (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, "Press SPACE to record, ESC to skip", 
                       (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"Movement: {movement_desc[:50]}", 
                       (10, 140), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            
            cv2.imshow('Sign Language Data Collection', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord(' '):  # SPACE to record
                if results.multi_hand_landmarks:
                    landmarks = self.extract_landmarks(results)
                    word_data.append({
                        'word': arabic_word,
                        'english': english_word,
                        'landmarks': landmarks,
                        'timestamp': datetime.now().isoformat()
                    })
                    sample_count += 1
                    print(f"✅ Sample {sample_count} recorded for '{english_word}'")
                else:
                    print("❌ No hands detected! Try again")
                    
            elif key == 27:  # ESC to skip
                print(f"⏭️ Skipped '{english_word}' with {sample_count} samples")
                break
        
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"✅ Completed '{english_word}' with {sample_count} samples")
        return word_data
    
    def save_data(self, data, filename):
        """Save data to JSON file"""
        filepath = os.path.join(self.data_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"💾 Data saved to: {filepath}")
    
    def collect_all_data(self, samples_per_word=30):
        """Collect data for all words"""
        all_data = []
        
        print("🤖 ARABIC SIGN LANGUAGE DATA COLLECTION")
        print("="*60)
        print("TARGET SENTENCE:")
        print("'Law al-alam kullu sakat, hat'amal eh?")
        print(" Hatkallam bi-yadi wa-hakhalli al-alam yasma'ni'")
        print("="*60)
        print(f"📊 COLLECTING {samples_per_word} SAMPLES FOR EACH OF {len(self.word_pairs)} WORDS")
        print("="*60)
        
        for i, (arabic_word, english_word, movement_desc) in enumerate(self.word_pairs, 1):
            print(f"\n🎯 WORD {i}/{len(self.word_pairs)}")
            print(f"Next: {english_word}")
            
            choice = input("Press Enter to continue, 's' to skip, 'q' to quit: ").lower().strip()
            if choice == 'q':
                print("🛑 Collection stopped by user")
                break
            elif choice == 's':
                print(f"⏭️ Skipped {english_word}")
                continue
            
            word_data = self.collect_word_data(arabic_word, english_word, movement_desc, samples_per_word)
            all_data.extend(word_data)
            
            # Save individual word data
            word_filename = f"{english_word.replace(' ', '_').replace('-', '_')}_data.json"
            self.save_data(word_data, word_filename)
        
        # Save all data combined
        if all_data:
            self.save_data(all_data, "all_signs_data.json")
            print(f"\n🎉 COLLECTION COMPLETE!")
            print(f"📊 Total samples collected: {len(all_data)}")
            print(f"📁 Data saved in: {self.data_dir}/")
        else:
            print("❌ No data collected")

    def show_word_list(self):
        """Show list of words to collect"""
        print("\n📋 WORDS TO COLLECT:")
        print("="*60)
        for i, (arabic, english, movement) in enumerate(self.word_pairs, 1):
            print(f"{i:2d}. {english}")
            print(f"    Arabic: {arabic}")
            print(f"    Movement: {movement}")
            print()

if __name__ == "__main__":
    collector = SignLanguageDataCollectorEnglish()
    
    print("🤖 ARABIC SIGN LANGUAGE DATA COLLECTION SYSTEM")
    print("="*60)
    print("This system will help you collect training data")
    print("for Arabic sign language recognition.")
    print("="*60)
    
    # Show word list
    collector.show_word_list()
    
    # Choose collection type
    print("📋 COLLECTION OPTIONS:")
    print("1. Collect data for ALL words")
    print("2. Collect data for ONE specific word")
    print("3. Show word list again")
    
    choice = input("\nChoose option (1-3): ").strip()
    
    if choice == "1":
        samples = input("Number of samples per word (default 30): ").strip()
        samples = int(samples) if samples.isdigit() else 30
        collector.collect_all_data(samples)
        
    elif choice == "2":
        print("\nAVAILABLE WORDS:")
        for i, (arabic, english, movement) in enumerate(collector.word_pairs, 1):
            print(f"{i}. {english}")
        
        word_idx = input("Choose word number: ").strip()
        if word_idx.isdigit() and 1 <= int(word_idx) <= len(collector.word_pairs):
            word_idx = int(word_idx) - 1
            arabic, english, movement = collector.word_pairs[word_idx]
            samples = input("Number of samples (default 30): ").strip()
            samples = int(samples) if samples.isdigit() else 30
            
            word_data = collector.collect_word_data(arabic, english, movement, samples)
            filename = f"{english.replace(' ', '_').replace('-', '_')}_data.json"
            collector.save_data(word_data, filename)
        else:
            print("❌ Invalid word number")
            
    elif choice == "3":
        collector.show_word_list()
    
    print("\n👋 Thank you for using the data collection system!")
    print("💡 Next step: Run 'python train_model.py' to train the model")
