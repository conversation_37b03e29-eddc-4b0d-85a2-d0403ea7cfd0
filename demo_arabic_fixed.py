#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Arabic Sign Language Demo with Fixed Arabic Text Display
عرض توضيحي لنظام التعرف على لغة الإشارة مع إصلاح عرض النص العربي
"""

import cv2
import mediapipe as mp
import numpy as np
import time
from PIL import Image, ImageDraw, ImageFont
import os

class ArabicSignDemoFixed:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Arabic words with English transliteration
        self.word_pairs = [
            ("السلام عليكم", "As-salamu alaykum"),
            ("وعليكم السلام ورحمة الله وبركاته", "Wa alaykumu s-salamu..."),
            ("لو", "Law"),
            ("العالم", "Al-alam"),
            ("كله", "Kullu"),
            ("سكت", "Sakat"),
            ("هتعمل", "Hat'amal"),
            ("ايه", "Eh"),
            ("هتكلم", "Hatkallam"),
            ("بايدي", "Bi-yadi"),
            ("وهخلي", "Wa-hakhalli"),
            ("يسمعني", "Yasma'ni")
        ]
        
        # Target sentence parts
        self.target_sentence = [
            ("لو", "Law"),
            ("العالم", "Al-alam"),
            ("كله", "Kullu"),
            ("سكت", "Sakat"),
            ("هتعمل", "Hat'amal"),
            ("ايه", "Eh"),
            ("هتكلم", "Hatkallam"),
            ("بايدي", "Bi-yadi"),
            ("وهخلي", "Wa-hakhalli"),
            ("العالم", "Al-alam"),
            ("يسمعني", "Yasma'ni")
        ]
        
        # Simulation variables
        self.current_word_index = 0
        self.last_change_time = time.time()
        self.change_interval = 3  # Change every 3 seconds
        
        # Try to load Arabic font
        self.arabic_font = None
        self.load_arabic_font()
        
    def load_arabic_font(self):
        """Try to load Arabic font"""
        font_paths = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/tahoma.ttf",
            "/System/Library/Fonts/Arial.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    self.arabic_font = ImageFont.truetype(font_path, 24)
                    print(f"✅ Loaded font: {font_path}")
                    break
                except:
                    continue
        
        if not self.arabic_font:
            print("⚠️ Could not load Arabic font, using default")
    
    def extract_landmarks(self, results):
        """Extract hand landmarks"""
        landmarks = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                hand_points = []
                for landmark in hand_landmarks.landmark:
                    hand_points.extend([landmark.x, landmark.y, landmark.z])
                landmarks.extend(hand_points)
        
        while len(landmarks) < 126:
            landmarks.append(0.0)
            
        return landmarks[:126]
    
    def simulate_prediction(self, landmarks):
        """Simulate prediction for target sentence"""
        current_time = time.time()
        
        # Change word every interval
        if current_time - self.last_change_time > self.change_interval:
            if self.current_word_index < len(self.target_sentence) - 1:
                self.current_word_index += 1
            else:
                self.current_word_index = 0  # Reset to beginning
            self.last_change_time = current_time
        
        # If hands detected, give high confidence
        if len([x for x in landmarks if x != 0.0]) > 10:
            confidence = 0.85 + np.random.random() * 0.1
            arabic_word, english_word = self.target_sentence[self.current_word_index]
            return arabic_word, english_word, confidence
        else:
            return "", "", 0.0
    
    def add_arabic_text_to_frame(self, frame, text, position, color=(255, 255, 255)):
        """Add Arabic text to frame using PIL"""
        if not self.arabic_font or not text:
            return frame
        
        try:
            # Convert frame to PIL Image
            frame_pil = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(frame_pil)
            
            # Add text
            draw.text(position, text, font=self.arabic_font, fill=color)
            
            # Convert back to OpenCV format
            frame = cv2.cvtColor(np.array(frame_pil), cv2.COLOR_RGB2BGR)
        except Exception as e:
            print(f"Error adding Arabic text: {e}")
        
        return frame
    
    def run_demo(self):
        """Run the demo"""
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ Cannot access camera")
            print("   Make sure camera is connected and not used by other apps")
            return
        
        print("🎥 Starting Arabic Sign Language Demo...")
        print("   Press ESC to exit")
        print("   Press R to clear sentence")
        print("   Press SPACE to manually add current word")
        
        sentence_ar = []
        sentence_en = []
        last_prediction = ""
        prediction_count = 0
        required_count = 10  # Required consecutive predictions
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            # Draw hand landmarks
            hands_detected = False
            if results.multi_hand_landmarks:
                hands_detected = True
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS,
                        self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2),
                        self.mp_drawing.DrawingSpec(color=(255, 0, 0), thickness=2)
                    )
            
            # Extract landmarks and predict
            landmarks = self.extract_landmarks(results)
            predicted_word_ar, predicted_word_en, confidence = self.simulate_prediction(landmarks)
            
            # Add info to frame
            self.add_info_to_frame(frame, predicted_word_ar, predicted_word_en, 
                                 confidence, hands_detected, sentence_ar, sentence_en)
            
            # Add to sentence if prediction is stable
            if predicted_word_ar and confidence > 0.8:
                if predicted_word_ar == last_prediction:
                    prediction_count += 1
                else:
                    prediction_count = 1
                    last_prediction = predicted_word_ar
                
                if prediction_count >= required_count:
                    if predicted_word_ar not in sentence_ar:
                        sentence_ar.append(predicted_word_ar)
                        sentence_en.append(predicted_word_en)
                        print(f"✅ Added: {predicted_word_ar} ({predicted_word_en})")
                    prediction_count = 0
            
            cv2.imshow('Arabic Sign Language Demo', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == 27:  # ESC
                break
            elif key == ord('r') or key == ord('R'):  # Clear sentence
                sentence_ar = []
                sentence_en = []
                print("🗑️ Sentence cleared")
            elif key == ord(' '):  # SPACE - manually add current word
                if predicted_word_ar and predicted_word_ar not in sentence_ar:
                    sentence_ar.append(predicted_word_ar)
                    sentence_en.append(predicted_word_en)
                    print(f"➕ Manually added: {predicted_word_ar} ({predicted_word_en})")
        
        cap.release()
        cv2.destroyAllWindows()
        
        if sentence_ar:
            print(f"\n📝 Final sentence (Arabic): {' '.join(sentence_ar)}")
            print(f"📝 Final sentence (English): {' '.join(sentence_en)}")
        
        print("👋 Demo finished")
    
    def add_info_to_frame(self, frame, prediction_ar, prediction_en, 
                         confidence, hands_detected, sentence_ar, sentence_en):
        """Add information to frame"""
        height, width = frame.shape[:2]
        
        # Semi-transparent background for text
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (width-10, 280), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Hand status
        if hands_detected:
            status_text = "✅ Hands Detected"
            status_color = (0, 255, 0)
        else:
            status_text = "❌ Place hands in front of camera"
            status_color = (0, 0, 255)
        
        cv2.putText(frame, status_text, (20, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        
        # Current prediction (English)
        if prediction_en and confidence > 0.7:
            cv2.putText(frame, f"Word: {prediction_en}", (20, 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(frame, f"Confidence: {confidence:.0%}", (20, 110),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # Try to add Arabic text using PIL
            if prediction_ar and self.arabic_font:
                frame = self.add_arabic_text_to_frame(frame, prediction_ar, (20, 130), (0, 255, 0))
            else:
                cv2.putText(frame, f"Arabic: {prediction_ar}", (20, 140),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        else:
            cv2.putText(frame, "Waiting for sign...", (20, 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
        
        # Current sentence (English)
        if sentence_en:
            sentence_text = " ".join(sentence_en)
            if len(sentence_text) > 40:
                sentence_text = sentence_text[:37] + "..."
            cv2.putText(frame, f"Sentence: {sentence_text}", (20, 180),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        else:
            cv2.putText(frame, "Sentence: (empty)", (20, 180),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (128, 128, 128), 2)
        
        # Progress
        progress = len(sentence_ar)
        total = len(self.target_sentence)
        cv2.putText(frame, f"Progress: {progress}/{total}", (20, 210),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # Instructions
        cv2.putText(frame, "ESC: Exit | R: Clear | SPACE: Add word", (20, 240),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Target sentence
        cv2.putText(frame, "Target: Law al-alam kullu sakat...", (20, 260),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)

def main():
    """Main function"""
    print("🤖 Arabic Sign Language Recognition Demo")
    print("   Fixed Arabic Text Display Version")
    print("="*50)
    print("Target sentence:")
    print("Arabic: لو العالم كله سكت، هتعمل ايه؟ هتكلم بايدي وهخلي العالم يسمعني")
    print("English: Law al-alam kullu sakat, hat'amal eh?")
    print("         Hatkallam bi-yadi wa-hakhalli al-alam yasma'ni")
    print("="*50)
    print("Instructions:")
    print("- Words will change automatically every 3 seconds")
    print("- Press SPACE to manually add the current word")
    print("- Press R to clear the sentence")
    print("- Press ESC to exit")
    print("="*50)
    
    try:
        demo = ArabicSignDemoFixed()
        demo.run_demo()
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Install required packages:")
        print("   pip install opencv-python mediapipe pillow")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
