@echo off
chcp 65001 >nul
title عرض توضيحي - نظام التعرف على لغة الإشارة

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║           🎮 عرض توضيحي سريع 🎮                             ║
echo ║                                                              ║
echo ║        نظام التعرف على لغة الإشارة العربية                  ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📦 تثبيت المكتبات الأساسية...
python -m pip install opencv-python mediapipe numpy

echo.
echo 🎥 بدء العرض التوضيحي...
echo.
echo تعليمات:
echo - ضع يديك أمام الكاميرا
echo - ستظهر الكلمات تلقائياً كل 3 ثوان
echo - اضغط ESC للخروج
echo - اضغط R لمسح الجملة
echo.

pause

python simple_demo.py

echo.
echo 👋 انتهى العرض التوضيحي
pause
