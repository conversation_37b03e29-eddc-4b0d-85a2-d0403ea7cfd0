<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التعرف على لغة الإشارة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .video-container {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            margin-bottom: 20px;
        }
        
        #video-stream {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .prediction-panel {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .sentence-panel {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .word-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .word-btn {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }
        
        .word-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .control-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .control-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .clear-btn {
            background: linear-gradient(45deg, #f44336, #d32f2f);
            color: white;
        }
        
        .clear-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 7px 20px rgba(244, 67, 54, 0.4);
        }
        
        .status-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .status-active {
            background: rgba(76, 175, 80, 0.9);
            color: white;
        }
        
        .status-inactive {
            background: rgba(244, 67, 54, 0.9);
            color: white;
        }
        
        .confidence-bar {
            width: 100%;
            height: 10px;
            background: rgba(255,255,255,0.3);
            border-radius: 5px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
            border-radius: 5px;
        }
        
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <h1 class="title">
                <i class="fas fa-hands"></i>
                نظام التعرف على لغة الإشارة
            </h1>
            
            <div class="row">
                <div class="col-lg-8">
                    <div class="video-container">
                        <img id="video-stream" src="{{ url_for('video_feed') }}" alt="بث مباشر">
                        <div id="status-indicator" class="status-indicator status-inactive">
                            <i class="fas fa-hand-paper"></i>
                            غير متصل
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="prediction-panel fade-in">
                        <h4><i class="fas fa-eye"></i> التنبؤ الحالي</h4>
                        <div id="current-prediction" style="font-size: 1.5rem; font-weight: bold;">
                            في انتظار الإشارة...
                        </div>
                        <div class="confidence-bar">
                            <div id="confidence-fill" class="confidence-fill" style="width: 0%"></div>
                        </div>
                        <div id="confidence-text" style="margin-top: 5px; font-size: 0.9rem;">
                            الثقة: 0%
                        </div>
                    </div>
                    
                    <div class="sentence-panel fade-in">
                        <h4><i class="fas fa-comment"></i> الجملة المكونة</h4>
                        <div id="sentence-display" style="font-size: 1.3rem; font-weight: bold; min-height: 50px;">
                            ابدأ بالإشارة لتكوين جملة...
                        </div>
                    </div>
                    
                    <div class="control-buttons">
                        <button class="control-btn clear-btn" onclick="clearSentence()">
                            <i class="fas fa-trash"></i>
                            مسح الجملة
                        </button>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <h5><i class="fas fa-plus-circle"></i> إضافة كلمات يدوياً</h5>
                        <div class="word-buttons">
                            <button class="word-btn" onclick="addWord('السلام عليكم')">السلام عليكم</button>
                            <button class="word-btn" onclick="addWord('وعليكم السلام ورحمة الله وبركاته')">وعليكم السلام ورحمة الله وبركاته</button>
                            <button class="word-btn" onclick="addWord('لو')">لو</button>
                            <button class="word-btn" onclick="addWord('العالم')">العالم</button>
                            <button class="word-btn" onclick="addWord('كله')">كله</button>
                            <button class="word-btn" onclick="addWord('سكت')">سكت</button>
                            <button class="word-btn" onclick="addWord('هتعمل')">هتعمل</button>
                            <button class="word-btn" onclick="addWord('ايه')">ايه</button>
                            <button class="word-btn" onclick="addWord('هتكلم')">هتكلم</button>
                            <button class="word-btn" onclick="addWord('بايدي')">بايدي</button>
                            <button class="word-btn" onclick="addWord('وهخلي')">وهخلي</button>
                            <button class="word-btn" onclick="addWord('يسمعني')">يسمعني</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث البيانات كل ثانية
        setInterval(updatePrediction, 1000);
        
        function updatePrediction() {
            fetch('/get_prediction')
                .then(response => response.json())
                .then(data => {
                    const predictionElement = document.getElementById('current-prediction');
                    const confidenceElement = document.getElementById('confidence-fill');
                    const confidenceText = document.getElementById('confidence-text');
                    const statusElement = document.getElementById('status-indicator');
                    const sentenceElement = document.getElementById('sentence-display');
                    
                    // تحديث حالة الاتصال
                    if (data.hands_detected) {
                        statusElement.className = 'status-indicator status-active';
                        statusElement.innerHTML = '<i class="fas fa-hand-paper"></i> يدين مكتشفة';
                    } else {
                        statusElement.className = 'status-indicator status-inactive';
                        statusElement.innerHTML = '<i class="fas fa-hand-paper"></i> لا توجد يدين';
                    }
                    
                    // تحديث التنبؤ
                    if (data.prediction && data.confidence > 0.7) {
                        predictionElement.textContent = data.prediction;
                        predictionElement.className = 'pulse';
                        
                        const confidencePercent = Math.round(data.confidence * 100);
                        confidenceElement.style.width = confidencePercent + '%';
                        confidenceText.textContent = `الثقة: ${confidencePercent}%`;
                    } else {
                        predictionElement.textContent = data.hands_detected ? 'غير واضح' : 'في انتظار الإشارة...';
                        predictionElement.className = '';
                        confidenceElement.style.width = '0%';
                        confidenceText.textContent = 'الثقة: 0%';
                    }
                    
                    // تحديث الجملة
                    if (data.sentence && data.sentence.length > 0) {
                        sentenceElement.textContent = data.sentence.join(' ');
                        sentenceElement.className = 'fade-in';
                    } else {
                        sentenceElement.textContent = 'ابدأ بالإشارة لتكوين جملة...';
                        sentenceElement.className = '';
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحديث البيانات:', error);
                });
        }
        
        function clearSentence() {
            fetch('/clear_sentence')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('sentence-display').textContent = 'ابدأ بالإشارة لتكوين جملة...';
                    }
                })
                .catch(error => {
                    console.error('خطأ في مسح الجملة:', error);
                });
        }
        
        function addWord(word) {
            fetch(`/add_word/${encodeURIComponent(word)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('sentence-display').textContent = data.sentence.join(' ');
                    }
                })
                .catch(error => {
                    console.error('خطأ في إضافة الكلمة:', error);
                });
        }
        
        // تحديث أولي
        updatePrediction();
    </script>
</body>
</html>
