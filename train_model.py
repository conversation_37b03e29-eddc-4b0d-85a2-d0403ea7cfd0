#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from model import SignLanguageModel
import matplotlib.pyplot as plt

def main():
    """الدالة الرئيسية لتدريب النموذج"""
    
    print("=" * 60)
    print("🤖 برنامج تدريب نموذج التعرف على لغة الإشارة")
    print("=" * 60)
    
    # التحقق من وجود البيانات
    data_file = "sign_data/all_signs_data.json"
    if not os.path.exists(data_file):
        print("❌ ملف البيانات غير موجود!")
        print(f"تأكد من وجود الملف: {data_file}")
        print("قم بتشغيل data_collection.py أولاً لجمع البيانات")
        return
    
    # إنشاء مجلد النماذج
    if not os.path.exists("models"):
        os.makedirs("models")
        print("📁 تم إنشاء مجلد النماذج")
    
    # إنشاء النموذج
    print("\n🔧 إنشاء نموذج التعرف على لغة الإشارة...")
    model = SignLanguageModel()
    
    try:
        # تدريب النموذج
        print("\n🚀 بدء عملية التدريب...")
        history = model.train_model(data_file)
        
        # حفظ النموذج
        print("\n💾 حفظ النموذج...")
        model.save_model()
        
        # رسم منحنيات التدريب
        print("\n📊 إنشاء رسوم بيانية للتدريب...")
        plot_training_history(history)
        
        print("\n✅ تم الانتهاء من التدريب بنجاح!")
        print("🎯 يمكنك الآن تشغيل التطبيق باستخدام: python app.py")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء التدريب: {str(e)}")
        return

def plot_training_history(history):
    """رسم منحنيات التدريب"""
    try:
        # إنشاء الرسم البياني
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # رسم دقة النموذج
        ax1.plot(history.history['accuracy'], label='دقة التدريب')
        ax1.plot(history.history['val_accuracy'], label='دقة التحقق')
        ax1.set_title('دقة النموذج')
        ax1.set_xlabel('العصر (Epoch)')
        ax1.set_ylabel('الدقة')
        ax1.legend()
        ax1.grid(True)
        
        # رسم خسارة النموذج
        ax2.plot(history.history['loss'], label='خسارة التدريب')
        ax2.plot(history.history['val_loss'], label='خسارة التحقق')
        ax2.set_title('خسارة النموذج')
        ax2.set_xlabel('العصر (Epoch)')
        ax2.set_ylabel('الخسارة')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        
        # حفظ الرسم البياني
        plots_dir = "plots"
        if not os.path.exists(plots_dir):
            os.makedirs(plots_dir)
        
        plt.savefig(os.path.join(plots_dir, "training_history.png"), dpi=300, bbox_inches='tight')
        print(f"📈 تم حفظ الرسم البياني في: {plots_dir}/training_history.png")
        
        # عرض الرسم البياني
        plt.show()
        
    except Exception as e:
        print(f"⚠️ تعذر إنشاء الرسم البياني: {str(e)}")

def check_data_quality():
    """فحص جودة البيانات"""
    import json
    
    data_file = "sign_data/all_signs_data.json"
    if not os.path.exists(data_file):
        return False
    
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"\n📊 إحصائيات البيانات:")
        print(f"   • إجمالي العينات: {len(data)}")
        
        # إحصائيات لكل كلمة
        word_counts = {}
        for sample in data:
            word = sample['word']
            word_counts[word] = word_counts.get(word, 0) + 1
        
        print(f"   • عدد الكلمات المختلفة: {len(word_counts)}")
        print(f"\n📝 توزيع العينات لكل كلمة:")
        
        for word, count in sorted(word_counts.items()):
            print(f"   • {word}: {count} عينة")
        
        # التحقق من الحد الأدنى للعينات
        min_samples = min(word_counts.values())
        if min_samples < 10:
            print(f"\n⚠️ تحذير: بعض الكلمات لديها عينات قليلة (أقل من 10)")
            print("   يُنصح بجمع المزيد من العينات لتحسين دقة النموذج")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔍 فحص البيانات...")
    if check_data_quality():
        print("✅ البيانات جاهزة للتدريب")
        
        # السؤال عن المتابعة
        response = input("\n❓ هل تريد المتابعة مع التدريب؟ (y/n): ").lower().strip()
        if response in ['y', 'yes', 'نعم', 'ن']:
            main()
        else:
            print("تم إلغاء التدريب")
    else:
        print("❌ البيانات غير جاهزة. قم بجمع البيانات أولاً باستخدام data_collection.py")
