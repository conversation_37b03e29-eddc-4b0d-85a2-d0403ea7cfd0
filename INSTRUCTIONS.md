# 🚀 تعليمات التشغيل السريع

## البدء السريع

### الطريقة الأولى - التشغيل التلقائي:
```bash
python quick_start.py
```

### الطريقة الثانية - التشغيل اليدوي:
```bash
# 1. تثبيت المكتبات
pip install -r requirements.txt

# 2. جمع البيانات
python data_collection.py

# 3. تدريب النموذج
python train_model.py

# 4. تشغيل التطبيق
python app.py
```

### الطريقة الثالثة - ويندوز:
```bash
# تشغيل الملف المجمع
start_system.bat
```

## 📋 خطوات العمل

### 1. جمع البيانات 📊
- شغل `data_collection.py`
- اختر "1" لجمع البيانات لجميع الكلمات
- لكل كلمة:
  - ضع يديك أمام الكاميرا
  - اعمل الإشارة المطلوبة
  - اضغط SPACE لتسجيل العينة
  - كرر 30-50 مرة لكل كلمة

### 2. تدريب النموذج 🚀
- شغل `train_model.py`
- انتظر انتهاء التدريب (5-15 دقيقة)
- سيتم حفظ النموذج تلقائياً

### 3. تشغيل التطبيق 🌐
- شغل `app.py`
- افتح المتصفح على `http://localhost:5000`
- استمتع بالتعرف على الإشارات!

## 🎯 الكلمات المدعومة

1. **السلام عليكم**
2. **وعليكم السلام ورحمة الله وبركاته**
3. **لو**
4. **العالم**
5. **كله**
6. **سكت**
7. **هتعمل**
8. **ايه**
9. **هتكلم**
10. **بايدي**
11. **وهخلي**
12. **يسمعني**

## 💡 نصائح مهمة

### لجمع البيانات:
- ✅ تأكد من الإضاءة الجيدة
- ✅ ضع يديك بوضوح أمام الكاميرا
- ✅ اعمل الإشارة ببطء ووضوح
- ✅ اجمع 30-50 عينة لكل كلمة
- ❌ تجنب الحركات السريعة
- ❌ تجنب الخلفيات المعقدة

### للتدريب:
- ⏰ التدريب يستغرق 5-15 دقيقة
- 💾 سيتم حفظ النموذج تلقائياً
- 📊 ستظهر رسوم بيانية للتدريب

### للاستخدام:
- 🎥 تأكد من عمل الكاميرا
- 🖐️ ضع يديك أمام الكاميرا
- ⏱️ اثبت على الإشارة لثوانٍ قليلة
- 📱 يمكن استخدام التطبيق للتصوير

## 🔧 حل المشاكل

### المشكلة: الكاميرا لا تعمل
**الحل:**
- تأكد من اتصال الكاميرا
- أغلق أي برامج أخرى تستخدم الكاميرا
- أعد تشغيل الجهاز

### المشكلة: خطأ في تثبيت المكتبات
**الحل:**
```bash
# حدث pip أولاً
python -m pip install --upgrade pip

# ثبت المكتبات واحدة واحدة
pip install opencv-python
pip install mediapipe
pip install tensorflow
pip install flask
```

### المشكلة: النموذج لا يتعرف على الإشارات
**الحل:**
- تأكد من جمع بيانات كافية (30+ عينة لكل كلمة)
- أعد تدريب النموذج
- تحسن الإضاءة والوضوح

### المشكلة: التطبيق بطيء
**الحل:**
- أغلق البرامج الأخرى
- تأكد من وجود ذاكرة كافية
- استخدم كاميرا بدقة أقل

## 📁 ملفات المشروع

```
doic/
├── 📄 quick_start.py          # التشغيل السريع
├── 📄 data_collection.py      # جمع البيانات
├── 📄 model.py               # نموذج التعرف
├── 📄 train_model.py         # تدريب النموذج
├── 📄 app.py                 # تطبيق الويب
├── 📄 requirements.txt       # المكتبات المطلوبة
├── 📄 start_system.bat       # تشغيل ويندوز
├── 📁 templates/             # ملفات HTML
├── 📁 sign_data/            # بيانات الإشارات
├── 📁 models/               # النماذج المدربة
└── 📁 plots/                # الرسوم البيانية
```

## 🎬 للتصوير

### إعدادات مثالية:
- 💡 إضاءة قوية ومتوازنة
- 🎥 كاميرا عالية الدقة
- 🖼️ خلفية نظيفة وبسيطة
- 👕 ملابس تتباين مع لون البشرة

### أثناء التصوير:
- 🎯 ركز على وضوح الإشارات
- ⏱️ اثبت على كل إشارة 2-3 ثوان
- 🔄 اعرض الجملة كاملة
- 📱 استخدم الأزرار اليدوية عند الحاجة

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع هذا الملف أولاً
2. تأكد من تثبيت جميع المتطلبات
3. جرب إعادة تشغيل النظام
4. تحقق من إعدادات الكاميرا

---

**ملاحظة:** هذا النظام مصمم للأغراض التعليمية والتطويرية. للاستخدام التجاري، يُنصح بتحسينات إضافية.
