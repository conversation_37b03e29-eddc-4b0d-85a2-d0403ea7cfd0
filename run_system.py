#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import time

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🤖 نظام التعرف على لغة الإشارة 🤖                    ║
    ║                                                              ║
    ║              مرحباً بك في النظام الذكي                      ║
    ║            للتعرف على لغة الإشارة العربية                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من Python
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    # التحقق من ملف المتطلبات
    if not os.path.exists("requirements.txt"):
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    print("✅ المتطلبات الأساسية متوفرة")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المكتبات المطلوبة...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ تم تثبيت المكتبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المكتبات: {e}")
        return False

def check_data():
    """التحقق من وجود البيانات"""
    data_file = "sign_data/all_signs_data.json"
    return os.path.exists(data_file)

def check_model():
    """التحقق من وجود النموذج المدرب"""
    model_file = "models/sign_language_model.h5"
    encoder_file = "models/label_encoder.pkl"
    return os.path.exists(model_file) and os.path.exists(encoder_file)

def run_data_collection():
    """تشغيل جمع البيانات"""
    print("\n📊 بدء جمع البيانات...")
    print("تعليمات:")
    print("- تأكد من وجود إضاءة جيدة")
    print("- ضع يديك بوضوح أمام الكاميرا")
    print("- اضغط SPACE لتسجيل كل إشارة")
    print("- اجمع 30-50 عينة لكل كلمة")
    
    input("\nاضغط Enter للمتابعة...")
    
    try:
        subprocess.run([sys.executable, "data_collection.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في جمع البيانات")
        return False

def run_training():
    """تشغيل تدريب النموذج"""
    print("\n🚀 بدء تدريب النموذج...")
    print("هذا قد يستغرق عدة دقائق...")
    
    try:
        subprocess.run([sys.executable, "train_model.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تدريب النموذج")
        return False

def run_app():
    """تشغيل التطبيق"""
    print("\n🌐 تشغيل تطبيق الويب...")
    print("سيتم فتح التطبيق على: http://localhost:5000")
    print("اضغط Ctrl+C للإيقاف")
    
    try:
        subprocess.run([sys.executable, "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف التطبيق")
    except subprocess.CalledProcessError:
        print("❌ فشل في تشغيل التطبيق")

def main_menu():
    """القائمة الرئيسية"""
    while True:
        print("\n" + "="*50)
        print("📋 القائمة الرئيسية")
        print("="*50)
        print("1. 📊 جمع البيانات")
        print("2. 🚀 تدريب النموذج")
        print("3. 🌐 تشغيل التطبيق")
        print("4. 🔄 تشغيل النظام كاملاً (تلقائي)")
        print("5. ❌ خروج")
        
        choice = input("\nاختر رقماً (1-5): ").strip()
        
        if choice == "1":
            run_data_collection()
        elif choice == "2":
            if not check_data():
                print("❌ لا توجد بيانات! قم بجمع البيانات أولاً")
                continue
            run_training()
        elif choice == "3":
            if not check_model():
                print("❌ النموذج غير مدرب! قم بتدريب النموذج أولاً")
                continue
            run_app()
        elif choice == "4":
            run_full_system()
            break
        elif choice == "5":
            print("👋 وداعاً!")
            break
        else:
            print("❌ اختيار غير صحيح")

def run_full_system():
    """تشغيل النظام كاملاً"""
    print("\n🔄 تشغيل النظام كاملاً...")
    
    # التحقق من البيانات
    if not check_data():
        print("📊 لا توجد بيانات، سيتم جمع البيانات أولاً...")
        if not run_data_collection():
            return
    else:
        print("✅ البيانات موجودة")
    
    # التحقق من النموذج
    if not check_model():
        print("🚀 النموذج غير مدرب، سيتم التدريب أولاً...")
        if not run_training():
            return
    else:
        print("✅ النموذج مدرب ومتاح")
    
    # تشغيل التطبيق
    print("🌐 تشغيل التطبيق...")
    run_app()

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # التحقق من المتطلبات
    if not check_requirements():
        return
    
    # تثبيت المكتبات
    print("📦 التحقق من المكتبات...")
    try:
        import cv2, mediapipe, tensorflow, flask
        print("✅ المكتبات متوفرة")
    except ImportError:
        print("📦 بعض المكتبات غير متوفرة، سيتم تثبيتها...")
        if not install_requirements():
            return
    
    # عرض حالة النظام
    print("\n📊 حالة النظام:")
    print(f"   • البيانات: {'✅ متوفرة' if check_data() else '❌ غير متوفرة'}")
    print(f"   • النموذج: {'✅ مدرب' if check_model() else '❌ غير مدرب'}")
    
    # عرض القائمة
    main_menu()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ حدث خطأ غير متوقع: {e}")
        print("يرجى التحقق من التثبيت والمحاولة مرة أخرى")
