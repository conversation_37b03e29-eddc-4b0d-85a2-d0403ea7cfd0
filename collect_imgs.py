#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Enhanced Arabic Sign Language Data Collection System
نظام جمع البيانات المحسن للغة الإشارة العربية
"""

import os
import cv2
import mediapipe as mp
import numpy as np
import json
from datetime import datetime
import time

# إعداد المجلدات للبيانات
DATA_DIR = './arabic_sign_data'
IMAGES_DIR = './arabic_sign_images'
LANDMARKS_DIR = './arabic_landmarks'

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [DATA_DIR, IMAGES_DIR, LANDMARKS_DIR]:
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"📁 تم إنشاء مجلد: {directory}")

# الكلمات العربية المستهدفة للجملة: "لو العالم كله سكت، هتعمل ايه؟ هتكلم بايدي وهخلي العالم يسمعني"
arabic_words = [
    ("السلام عليكم", "As-salamu alaykum", "اليد اليمنى مفتوحة تتحرك من اليمين لليسار + اليد اليسرى على القلب"),
    ("وعليكم السلام ورحمة الله وبركاته", "Wa alaykumu s-salamu", "كلتا اليدين مفتوحتين، حركة دائرية للأعلى والخارج"),
    ("لو", "Law", "السبابة للأعلى، حركة يمين-يسار (استفهام)"),
    ("العالم", "Al-alam", "اليدين في شكل كرة، حركة دائرية حول بعضهما"),
    ("كله", "Kullu", "افتح اليدين للخارج ثم اجمعهما"),
    ("سكت", "Sakat", "السبابة على الشفاه (إشارة الصمت)"),
    ("هتعمل", "Hat'amal", "قبضتين تتحركان للأمام والخلف (حركة العمل)"),
    ("ايه", "Eh", "ارفع اليدين بأصابع مفتوحة (استفهام)"),
    ("هتكلم", "Hatkallam", "اليد أمام الفم مع تحريك الأصابع (كلام)"),
    ("بايدي", "Bi-yadi", "ارفع كلتا اليدين مع فتح الكفين"),
    ("وهخلي", "Wa-hakhalli", "من قبضة إلى يد مفتوحة للأمام (عطاء/صنع)"),
    ("يسمعني", "Yasma'ni", "اليد خلف الأذن ثم للأمام (استماع)")
]

dataset_size = 50  # عدد الصور لكل كلمة

# إعداد MediaPipe لاستخراج النقاط المميزة لليدين
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(
    static_image_mode=False,
    max_num_hands=2,
    min_detection_confidence=0.8,
    min_tracking_confidence=0.7
)
mp_drawing = mp.solutions.drawing_utils

def extract_landmarks(results):
    """استخراج النقاط المميزة من اليدين"""
    landmarks = []

    if results.multi_hand_landmarks:
        for hand_landmarks in results.multi_hand_landmarks:
            hand_points = []
            for landmark in hand_landmarks.landmark:
                hand_points.extend([landmark.x, landmark.y, landmark.z])
            landmarks.extend(hand_points)

    # ملء بالأصفار إذا كان أقل من يدين
    while len(landmarks) < 126:  # 21 نقطة × 3 إحداثيات × 2 يد
        landmarks.append(0.0)

    return landmarks[:126]

def add_info_overlay(frame, arabic_word, english_word, movement_desc, collected, total, hands_detected):
    """إضافة معلومات على الإطار"""
    height, width = frame.shape[:2]

    # خلفية شبه شفافة
    overlay = frame.copy()
    cv2.rectangle(overlay, (10, 10), (width-10, 220), (0, 0, 0), -1)
    cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)

    # الكلمة العربية (خط أكبر)
    cv2.putText(frame, arabic_word, (20, 50),
               cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 255), 3)

    # الكلمة الإنجليزية
    cv2.putText(frame, english_word, (20, 85),
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

    # وصف الحركة
    cv2.putText(frame, movement_desc[:50], (20, 115),
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 2)
    if len(movement_desc) > 50:
        cv2.putText(frame, movement_desc[50:], (20, 135),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 2)

    # التقدم
    cv2.putText(frame, f"Progress: {collected}/{total}", (20, 165),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    # حالة اكتشاف اليدين
    if hands_detected:
        cv2.putText(frame, "Hands: DETECTED", (20, 190),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    else:
        cv2.putText(frame, "Hands: NOT DETECTED", (20, 190),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

    # التعليمات
    cv2.putText(frame, "SPACE: Capture | ESC: Next Word | Q: Quit", (20, height-20),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

# فتح كاميرا الويب
cap = cv2.VideoCapture(0)

if not cap.isOpened():
    print("❌ Cannot access camera")
    exit()

print("🤖 نظام جمع البيانات للغة الإشارة العربية")
print("="*70)
print("الجملة المستهدفة:")
print("'لو العالم كله سكت، هتعمل ايه؟")
print(" هتكلم بايدي وهخلي العالم يسمعني'")
print("="*70)
print(f"📊 سيتم جمع {dataset_size} صورة لكل من {len(arabic_words)} كلمة")
print(f"📸 الصور ستُحفظ في: {IMAGES_DIR}/")
print(f"📊 البيانات ستُحفظ في: {LANDMARKS_DIR}/")
print("="*70)

all_data = []

# جمع البيانات لكل كلمة عربية
for word_index, (arabic_word, english_word, movement_desc) in enumerate(arabic_words):
    # إنشاء مجلد لكل فئة إذا لم يكن موجودًا
    if not os.path.exists(os.path.join(DATA_DIR, f'ar_{j}')):
        os.makedirs(os.path.join(DATA_DIR, f'ar_{j}'))

    # تحديد اسم الفئة
    if j == number_of_classes_ar:
        class_name = "Right Hand (Arabic)"
    elif j == number_of_classes_ar + 1:
        class_name = "Left Hand (English)"
    else:
        class_name = f"Arabic Class {j}"

    print(f'Collecting data for {class_name}')

    # انتظار المستخدم للضغط على "Q" لبدء جمع الصور
    while True:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame. Exiting...")
            break

        cv2.putText(frame, f'Ready to collect {class_name}? Press "Q" to start!', (50, 50),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.imshow('frame', frame)

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    # جمع الصور للفئة الحالية
    counter = 0
    while counter < dataset_size:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame. Exiting...")
            break

        cv2.imshow('frame', frame)
        cv2.imwrite(os.path.join(DATA_DIR, f'ar_{j}', f'{counter}.jpg'), frame)
        counter += 1

        if cv2.waitKey(1) & 0xFF == ord('q'):  # السماح بالخروج أثناء جمع الصور
            print("Exiting early...")
            break

# جمع البيانات للحروف والأرقام الإنجليزية
for j in range(number_of_classes_en):
    # إنشاء مجلد لكل فئة إذا لم يكن موجودًا
    if not os.path.exists(os.path.join(DATA_DIR, f'en_{j}')):
        os.makedirs(os.path.join(DATA_DIR, f'en_{j}'))

    # تحديد اسم الفئة
    class_name = f"English Class {j}"

    print(f'Collecting data for {class_name}')

    # انتظار المستخدم للضغط على "Q" لبدء جمع الصور
    while True:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame. Exiting...")
            break

        cv2.putText(frame, f'Ready to collect {class_name}? Press "Q" to start!', (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.imshow('frame', frame)

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    # جمع الصور للفئة الحالية
    counter = 0
    while counter < dataset_size:
        ret, frame = cap.read()
        if not ret:
            print("Failed to grab frame. Exiting...")
            break

        cv2.imshow('frame', frame)
        cv2.imwrite(os.path.join(DATA_DIR, f'en_{j}', f'{counter}.jpg'), frame)
        counter += 1

        if cv2.waitKey(1) & 0xFF == ord('q'):  # السماح بالخروج أثناء جمع الصور
            print("Exiting early...")
            break

cap.release()
cv2.destroyAllWindows()
