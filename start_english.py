#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
English Version - Sign Language Recognition System
نسخة إنجليزية - نظام التعرف على لغة الإشارة
"""

import os
import sys
import subprocess

def print_banner():
    """Print welcome banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🤖 Arabic Sign Language Recognition System 🤖         ║
    ║                                                              ║
    ║              Welcome to the Smart System                     ║
    ║            for Arabic Sign Language Recognition              ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Check Python version"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8+ required")
        return False
    print(f"✅ Python {sys.version.split()[0]} - Compatible")
    return True

def install_requirements():
    """Install requirements"""
    print("\n📦 Installing required packages...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Packages installed successfully")
            return True
        else:
            print("❌ Failed to install packages")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Installation error: {e}")
        return False

def check_camera():
    """Check camera"""
    print("\n📷 Checking camera...")
    
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            if ret:
                print("✅ Camera is working properly")
                return True
            else:
                print("⚠️ Camera connected but no image")
                return False
        else:
            print("❌ Cannot access camera")
            return False
    except ImportError:
        print("⚠️ OpenCV not installed - will be installed")
        return True
    except Exception as e:
        print(f"❌ Camera error: {e}")
        return False

def show_menu():
    """Show main menu"""
    print("\n" + "="*50)
    print("📋 Choose what you want to do:")
    print("="*50)
    print("1. 📊 Data Collection")
    print("2. 🚀 Train Model")
    print("3. 🌐 Run Web Application")
    print("4. 🔄 Full Setup (Automatic)")
    print("5. 🎮 Simple Demo")
    print("6. ❌ Exit")
    print("="*50)

def run_data_collection():
    """Run data collection"""
    print("\n📊 Starting data collection...")
    print("Instructions:")
    print("- Make sure you have good lighting")
    print("- Place your hands clearly in front of the camera")
    print("- Press SPACE to record each sign")
    print("- Collect 30-50 samples per word")
    
    input("\nPress Enter to continue...")
    
    try:
        subprocess.run([sys.executable, "data_collection.py"], check=True)
    except Exception as e:
        print(f"❌ Error: {e}")

def run_training():
    """Run model training"""
    print("\n🚀 Starting model training...")
    print("This may take several minutes...")
    
    try:
        subprocess.run([sys.executable, "train_model.py"], check=True)
    except Exception as e:
        print(f"❌ Error: {e}")

def run_app():
    """Run web application"""
    print("\n🌐 Starting web application...")
    print("Application will open at: http://localhost:5000")
    print("Press Ctrl+C to stop")
    
    try:
        subprocess.run([sys.executable, "app.py"], check=True)
    except Exception as e:
        print(f"❌ Error: {e}")

def run_demo():
    """Run simple demo"""
    print("\n🎮 Starting simple demo...")
    print("Instructions:")
    print("- Place your hands in front of the camera")
    print("- Words will appear automatically every 3 seconds")
    print("- Press ESC to exit")
    print("- Press R to clear sentence")
    
    input("\nPress Enter to continue...")
    
    try:
        subprocess.run([sys.executable, "simple_demo.py"], check=True)
    except Exception as e:
        print(f"❌ Error: {e}")

def full_setup():
    """Full automatic setup"""
    print("\n🔄 Starting full setup...")
    
    # Check for data
    if not os.path.exists("sign_data/all_signs_data.json"):
        print("\n📊 No data found, collecting data first...")
        run_data_collection()
    
    # Check for model
    if not (os.path.exists("models/sign_language_model.h5") and 
            os.path.exists("models/label_encoder.pkl")):
        print("\n🚀 Model not trained, training first...")
        run_training()
    
    # Run application
    print("\n🌐 Running application...")
    run_app()

def show_supported_words():
    """Show supported words"""
    print("\n🎯 Supported Words (13 words):")
    print("="*40)
    words = [
        "1. As-salamu alaykum (السلام عليكم)",
        "2. Wa alaykumu s-salamu wa-rahmatu llahi wa-barakatuh",
        "3. Law (لو)",
        "4. Al-alam (العالم)", 
        "5. Kullu (كله)",
        "6. Sakat (سكت)",
        "7. Hat'amal (هتعمل)",
        "8. Eh (ايه)",
        "9. Hatkallam (هتكلم)",
        "10. Bi-yadi (بايدي)",
        "11. Wa-hakhalli (وهخلي)",
        "12. Yasma'ni (يسمعني)"
    ]
    
    for word in words:
        print(f"   {word}")
    
    print("\n🎯 Target Sentence:")
    print('   "Law al-alam kullu sakat, hat\'amal eh?')
    print('    Hatkallam bi-yadi wa-hakhalli al-alam yasma\'ni"')

def main():
    """Main function"""
    print_banner()
    
    # Check Python
    if not check_python_version():
        input("Press Enter to exit...")
        return
    
    # Check camera
    camera_ok = check_camera()
    if not camera_ok:
        print("⚠️ Warning: You may face camera issues")
    
    # Check packages
    try:
        import cv2, mediapipe, tensorflow, flask
        print("✅ All packages available")
    except ImportError:
        print("📦 Some packages missing, installing...")
        if not install_requirements():
            print("❌ Failed to install packages")
            input("Press Enter to exit...")
            return
    
    # Show supported words
    show_supported_words()
    
    # Main menu
    while True:
        show_menu()
        choice = input("\nChoose a number (1-6): ").strip()
        
        if choice == "1":
            run_data_collection()
        elif choice == "2":
            run_training()
        elif choice == "3":
            run_app()
        elif choice == "4":
            full_setup()
            break
        elif choice == "5":
            run_demo()
        elif choice == "6":
            print("\n👋 Thank you for using the system!")
            break
        else:
            print("❌ Invalid choice")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 System stopped by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
