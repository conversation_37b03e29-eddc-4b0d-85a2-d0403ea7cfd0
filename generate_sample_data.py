#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Generate Sample Data for Sign Language Recognition
توليد بيانات تجريبية لنظام التعرف على لغة الإشارة
"""

import os
import json
import numpy as np
import cv2
from datetime import datetime
import random

class SampleDataGenerator:
    def __init__(self):
        # Word pairs: (Arabic, English, Movement Description)
        self.word_pairs = [
            ("السلام عليكم", "As-salamu alaykum", "Right hand open, move left to right + left hand on heart"),
            ("وعليكم السلام ورحمة الله وبركاته", "Wa alaykumu s-salamu", "Both hands open, circular motion up and out"),
            ("لو", "Law", "Index finger up, move left-right (questioning)"),
            ("العالم", "Al-alam", "Hands in ball shape, circular motion around each other"),
            ("كله", "<PERSON><PERSON><PERSON>", "Open hands wide outward, then bring together"),
            ("سكت", "Sakat", "Index finger on lips (silence gesture)"),
            ("هتعمل", "Hat'amal", "Two fists moving back and forth (working motion)"),
            ("ايه", "Eh", "Raise hands up with open fingers (questioning)"),
            ("هتكلم", "Hatkallam", "Hand in front of mouth, move fingers (talking)"),
            ("بايدي", "Bi-yadi", "Raise both hands with open palms"),
            ("وهخلي", "Wa-hakhalli", "From fist to open hand forward (giving/making)"),
            ("يسمعني", "Yasma'ni", "Hand behind ear then forward (listening)")
        ]
        
        # Create directories
        self.data_dir = "sign_data"
        self.images_dir = "sign_images"
        self.landmarks_dir = "landmarks_data"
        
        for directory in [self.data_dir, self.images_dir, self.landmarks_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 Created directory: {directory}")
    
    def generate_realistic_landmarks(self, word_type="default"):
        """Generate realistic hand landmarks based on word type"""
        landmarks = []
        
        # Base hand positions (normalized coordinates 0-1)
        if word_type == "greeting":  # السلام عليكم
            # Right hand raised, left hand on chest
            right_hand_base = [0.7, 0.3]  # Upper right
            left_hand_base = [0.3, 0.6]   # Lower left (chest area)
        elif word_type == "questioning":  # لو، ايه
            # Both hands raised with questioning gesture
            right_hand_base = [0.6, 0.2]  # Upper center-right
            left_hand_base = [0.4, 0.2]   # Upper center-left
        elif word_type == "silence":  # سكت
            # One hand near mouth
            right_hand_base = [0.5, 0.4]  # Center (mouth area)
            left_hand_base = [0.3, 0.7]   # Lower left
        elif word_type == "work":  # هتعمل
            # Both hands in working position
            right_hand_base = [0.6, 0.5]  # Center-right
            left_hand_base = [0.4, 0.5]   # Center-left
        elif word_type == "talk":  # هتكلم
            # Hand near mouth
            right_hand_base = [0.5, 0.4]  # Center (mouth area)
            left_hand_base = [0.3, 0.6]   # Lower left
        elif word_type == "listen":  # يسمعني
            # Hand near ear
            right_hand_base = [0.7, 0.3]  # Upper right (ear area)
            left_hand_base = [0.3, 0.6]   # Lower left
        else:  # default
            # Standard position
            right_hand_base = [0.6, 0.4]  # Center-right
            left_hand_base = [0.4, 0.4]   # Center-left
        
        # Generate 21 landmarks for each hand (42 total)
        for hand_base in [right_hand_base, left_hand_base]:
            for i in range(21):  # 21 landmarks per hand
                # Add some realistic variation around base position
                x = hand_base[0] + random.uniform(-0.1, 0.1)
                y = hand_base[1] + random.uniform(-0.1, 0.1)
                z = random.uniform(-0.05, 0.05)  # Depth variation
                
                # Ensure coordinates stay within bounds
                x = max(0.0, min(1.0, x))
                y = max(0.0, min(1.0, y))
                
                landmarks.extend([x, y, z])
        
        # Pad to 126 elements (21 points × 3 coordinates × 2 hands)
        while len(landmarks) < 126:
            landmarks.append(0.0)
        
        return landmarks[:126]
    
    def generate_sample_image(self, word_english, sample_id):
        """Generate a sample image with text overlay"""
        # Create a simple image with text
        img = np.ones((480, 640, 3), dtype=np.uint8) * 50  # Dark background
        
        # Add word text
        cv2.putText(img, f"Word: {word_english}", (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(img, f"Sample: {sample_id}", (50, 150), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (200, 200, 200), 2)
        
        # Add some hand-like shapes (simple rectangles)
        # Right hand
        cv2.rectangle(img, (400, 200), (500, 350), (0, 255, 0), 2)
        cv2.putText(img, "Right Hand", (410, 190), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # Left hand
        cv2.rectangle(img, (150, 250), (250, 400), (0, 255, 0), 2)
        cv2.putText(img, "Left Hand", (160, 240), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # Add timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cv2.putText(img, timestamp, (50, 450), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (100, 100, 100), 1)
        
        return img
    
    def determine_word_type(self, arabic_word):
        """Determine word type for realistic landmark generation"""
        if "السلام" in arabic_word or "وعليكم" in arabic_word:
            return "greeting"
        elif arabic_word in ["لو", "ايه"]:
            return "questioning"
        elif arabic_word == "سكت":
            return "silence"
        elif arabic_word == "هتعمل":
            return "work"
        elif arabic_word == "هتكلم":
            return "talk"
        elif arabic_word == "يسمعني":
            return "listen"
        else:
            return "default"
    
    def generate_word_data(self, arabic_word, english_word, movement_desc, samples_count=30):
        """Generate sample data for one word"""
        print(f"\n📊 Generating data for: {english_word} ({arabic_word})")
        print(f"   Movement: {movement_desc}")
        print(f"   Samples: {samples_count}")
        
        word_data = []
        word_type = self.determine_word_type(arabic_word)
        
        for sample_id in range(1, samples_count + 1):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            
            # Generate realistic landmarks
            landmarks = self.generate_realistic_landmarks(word_type)
            
            # Generate sample image
            img = self.generate_sample_image(english_word, sample_id)
            
            # Save image
            img_filename = f"{english_word.replace(' ', '_')}_{sample_id:03d}_{timestamp}.jpg"
            img_path = os.path.join(self.images_dir, img_filename)
            cv2.imwrite(img_path, img)
            
            # Create landmark data
            landmark_data = {
                'word_arabic': arabic_word,
                'word_english': english_word,
                'sample_id': sample_id,
                'timestamp': timestamp,
                'image_filename': img_filename,
                'landmarks': landmarks,
                'hand_detected': True,  # All samples have hands detected
                'movement_description': movement_desc,
                'word_type': word_type
            }
            
            # Save individual landmark file
            landmark_filename = f"{english_word.replace(' ', '_')}_{sample_id:03d}_{timestamp}.json"
            landmark_path = os.path.join(self.landmarks_dir, landmark_filename)
            
            with open(landmark_path, 'w', encoding='utf-8') as f:
                json.dump(landmark_data, f, ensure_ascii=False, indent=2)
            
            # Add to word data
            word_data.append({
                'word_arabic': arabic_word,
                'word_english': english_word,
                'sample_id': sample_id,
                'landmarks': landmarks,
                'image_path': img_path,
                'landmark_path': landmark_path,
                'timestamp': datetime.now().isoformat(),
                'movement_description': movement_desc,
                'hand_detected': True  # All generated samples have hands
            })
        
        print(f"   ✅ Generated {len(word_data)} samples")
        return word_data
    
    def generate_all_data(self, samples_per_word=30):
        """Generate sample data for all words"""
        print("🤖 GENERATING SAMPLE DATA FOR ARABIC SIGN LANGUAGE")
        print("="*70)
        print("TARGET SENTENCE:")
        print("'Law al-alam kullu sakat, hat'amal eh?")
        print(" Hatkallam bi-yadi wa-hakhalli al-alam yasma'ni'")
        print("="*70)
        print(f"📊 GENERATING {samples_per_word} SAMPLES FOR EACH OF {len(self.word_pairs)} WORDS")
        print(f"📸 Images will be saved in: {self.images_dir}/")
        print(f"📊 Landmarks will be saved in: {self.landmarks_dir}/")
        print("="*70)
        
        all_data = []
        
        for i, (arabic_word, english_word, movement_desc) in enumerate(self.word_pairs, 1):
            print(f"\n🎯 WORD {i}/{len(self.word_pairs)}")
            
            word_data = self.generate_word_data(arabic_word, english_word, movement_desc, samples_per_word)
            all_data.extend(word_data)
            
            # Save individual word data
            word_filename = f"{english_word.replace(' ', '_').replace('-', '_')}_complete_data.json"
            word_filepath = os.path.join(self.data_dir, word_filename)
            with open(word_filepath, 'w', encoding='utf-8') as f:
                json.dump(word_data, f, ensure_ascii=False, indent=2)
        
        # Save combined data
        combined_filepath = os.path.join(self.data_dir, "all_signs_complete_data.json")
        with open(combined_filepath, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=2)
        
        # Generate summary
        self.generate_summary(all_data)
        
        return all_data
    
    def generate_summary(self, all_data):
        """Generate generation summary"""
        print(f"\n🎉 DATA GENERATION COMPLETE!")
        print("="*70)
        print(f"📊 Total samples generated: {len(all_data)}")
        print(f"📸 Images saved in: {self.images_dir}/")
        print(f"📊 Landmarks saved in: {self.landmarks_dir}/")
        print(f"📁 Combined data saved in: {self.data_dir}/")
        
        # Count samples per word
        word_counts = {}
        for sample in all_data:
            word = sample['word_english']
            word_counts[word] = word_counts.get(word, 0) + 1
        
        print("\n📋 Samples per word:")
        for word, count in word_counts.items():
            print(f"   {word}: {count} samples")
        
        print("\n💡 Next steps:")
        print("1. Run 'python analyze_collected_data.py' to analyze the data")
        print("2. Review the generated images in the 'sign_images' folder")
        print("3. Check landmark data in the 'landmarks_data' folder")
        print("4. Run 'python train_model.py' to train the model")

if __name__ == "__main__":
    generator = SampleDataGenerator()
    
    print("🤖 SAMPLE DATA GENERATOR FOR ARABIC SIGN LANGUAGE")
    print("="*70)
    print("This will generate realistic sample data for testing")
    print("the sign language recognition system.")
    print("="*70)
    
    samples = input("Number of samples per word (default 30): ").strip()
    samples = int(samples) if samples.isdigit() else 30
    
    print(f"\n🚀 Starting generation of {samples} samples per word...")
    generator.generate_all_data(samples)
    
    print("\n👋 Sample data generation complete!")
    print("💡 You can now test the analysis and training systems!")
