#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
عرض توضيحي مبسط لنظام التعرف على لغة الإشارة
Simple Demo for Sign Language Recognition System
"""

import cv2
import mediapipe as mp
import numpy as np
import time

class SimpleSignDemo:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # الكلمات المدعومة
        self.words = [
            "السلام عليكم",
            "وعليكم السلام ورحمة الله وبركاته", 
            "لو", "العالم", "كله", "سكت", 
            "هتعمل", "ايه", "هتكلم", "بايدي", 
            "وهخلي", "يسمعني"
        ]
        
        # محاكاة التنبؤات (للعرض التوضيحي)
        self.current_word_index = 0
        self.last_change_time = time.time()
        self.change_interval = 3  # تغيير كل 3 ثوان
        
    def extract_landmarks(self, results):
        """استخراج النقاط المميزة من اليدين"""
        landmarks = []
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                hand_points = []
                for landmark in hand_landmarks.landmark:
                    hand_points.extend([landmark.x, landmark.y, landmark.z])
                landmarks.extend(hand_points)
        
        while len(landmarks) < 126:  # 21 نقطة × 3 إحداثيات × 2 يد
            landmarks.append(0.0)
            
        return landmarks[:126]
    
    def simulate_prediction(self, landmarks):
        """محاكاة التنبؤ (للعرض التوضيحي)"""
        current_time = time.time()
        
        # تغيير الكلمة كل فترة
        if current_time - self.last_change_time > self.change_interval:
            self.current_word_index = (self.current_word_index + 1) % len(self.words)
            self.last_change_time = current_time
        
        # إذا كانت اليدين مكتشفة، أعطي ثقة عالية
        if len([x for x in landmarks if x != 0.0]) > 10:
            confidence = 0.85 + np.random.random() * 0.1  # ثقة بين 85-95%
            return self.words[self.current_word_index], confidence
        else:
            return "", 0.0
    
    def run_demo(self):
        """تشغيل العرض التوضيحي"""
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ لا يمكن الوصول للكاميرا")
            print("   Cannot access camera")
            return
        
        print("🎥 بدء العرض التوضيحي...")
        print("   Starting demo...")
        print("   اضغط ESC للخروج / Press ESC to exit")
        
        sentence = []
        last_prediction = ""
        prediction_count = 0
        required_count = 15  # عدد التنبؤات المتتالية المطلوبة
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame = cv2.flip(frame, 1)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)
            
            # رسم النقاط المميزة
            hands_detected = False
            if results.multi_hand_landmarks:
                hands_detected = True
                for hand_landmarks in results.multi_hand_landmarks:
                    self.mp_drawing.draw_landmarks(
                        frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS,
                        self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2),
                        self.mp_drawing.DrawingSpec(color=(255, 0, 0), thickness=2)
                    )
            
            # استخراج النقاط والتنبؤ
            landmarks = self.extract_landmarks(results)
            predicted_word, confidence = self.simulate_prediction(landmarks)
            
            # إضافة معلومات على الإطار
            self.add_info_to_frame(frame, predicted_word, confidence, hands_detected, sentence)
            
            # إضافة للجملة إذا كان التنبؤ مستقر
            if predicted_word and confidence > 0.8:
                if predicted_word == last_prediction:
                    prediction_count += 1
                else:
                    prediction_count = 1
                    last_prediction = predicted_word
                
                if prediction_count >= required_count:
                    if predicted_word not in sentence:
                        sentence.append(predicted_word)
                        print(f"✅ تمت إضافة: {predicted_word}")
                    prediction_count = 0
            
            cv2.imshow('Sign Language Demo - عرض توضيحي لغة الإشارة', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == 27:  # ESC
                break
            elif key == ord('r') or key == ord('R'):  # مسح الجملة
                sentence = []
                print("🗑️ تم مسح الجملة")
        
        cap.release()
        cv2.destroyAllWindows()
        
        if sentence:
            print(f"\n📝 الجملة النهائية: {' '.join(sentence)}")
        
        print("👋 انتهى العرض التوضيحي")
    
    def add_info_to_frame(self, frame, prediction, confidence, hands_detected, sentence):
        """إضافة المعلومات على الإطار"""
        height, width = frame.shape[:2]
        
        # خلفية شفافة للنص
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (width-10, 200), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # حالة اليدين
        if hands_detected:
            status_text = "✅ يدين مكتشفة"
            status_color = (0, 255, 0)
        else:
            status_text = "❌ ضع يديك أمام الكاميرا"
            status_color = (0, 0, 255)
        
        cv2.putText(frame, status_text, (20, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        
        # التنبؤ الحالي
        if prediction and confidence > 0.7:
            cv2.putText(frame, f"الكلمة: {prediction}", (20, 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(frame, f"الثقة: {confidence:.0%}", (20, 110),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        else:
            cv2.putText(frame, "في انتظار الإشارة...", (20, 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
        
        # الجملة المكونة
        if sentence:
            sentence_text = " ".join(sentence)
            # تقسيم النص إذا كان طويلاً
            if len(sentence_text) > 40:
                sentence_text = sentence_text[:37] + "..."
            cv2.putText(frame, f"الجملة: {sentence_text}", (20, 150),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        else:
            cv2.putText(frame, "الجملة: (فارغة)", (20, 150),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (128, 128, 128), 2)
        
        # تعليمات
        cv2.putText(frame, "ESC: خروج | R: مسح الجملة", (20, 180),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

def main():
    """الدالة الرئيسية"""
    print("🤖 عرض توضيحي لنظام التعرف على لغة الإشارة")
    print("   Sign Language Recognition Demo")
    print("="*50)
    
    try:
        demo = SimpleSignDemo()
        demo.run_demo()
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("   تأكد من تثبيت: pip install opencv-python mediapipe")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
